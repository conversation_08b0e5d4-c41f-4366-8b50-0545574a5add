## 1. 引言

### 1.1 项目概述

跨境运营助手是一款旨在帮助跨境电商运营人员和品牌营销人员简化网红（或称创作者/KOL）建联流程的Web应用程序。它利用AI技术分析产品特性，智能匹配合适的海外创作者（特别是YouTube博主），管理建联过程，并追踪合作效果，从而提高网红营销的效率和成功率。

### 1.2 产品路径：

**产品目标**：推广商品》推广品牌》接管商家推广全流程

**产品路径**：自动化匹配》自动化建联》Ai化建联》》企业Ai投放Agent

**商业化**：卖网红信息》卖 Ai建联服务订阅》抽推广服务佣金

### 1.3 项目目标

- **提高效率：** 自动化产品分析、创作者匹配和初步沟通邮件生成，减少人工搜索和筛选时间。
- **提升匹配精准度：** 利用AI分析产品与创作者内容、受众的契合度，推荐更合适的合作人选。
- **简化管理：** 提供集中的平台来管理产品信息、创作者资料、建联进度和沟通记录。
- **数据驱动决策：** 通过仪表盘和（未来规划的）数据分析功能，帮助用户了解合作效果，优化策略。

---

### 2. 功能需求

### 2.1 用户认证与授权

- **2.1.1 登录:**
    - 目前只提供谷歌账户的登录方式：
        - **Google账号登录:**
            - 首次进入时，使用任何功能和按钮，都跳转到谷歌登录界面，点击【使用 google 账号登录】后跳转至Google账号选择页面。
            - 接入谷歌的 **OAuth 2.0 api**，获取用户授权及必要的 `access_token` 和 `refresh_token`，同下方邮箱账号绑定。
            - 权限需要用户的邮箱管理权限，获取读取邮箱，存储用户的头像和谷歌名称，系统中用此头像和名称进行显示。
            - 安全存储传输用户凭证和Token。
- **2.1.2 多邮箱账户管理:**
    - **邮箱配置入口：**
        - 在账户设置页面提供"邮箱配置"功能入口
        - 支持添加和管理多种类型的邮箱账号
    - **邮箱类型支持：**
        - **QQ邮箱：** 自动配置SMTP/IMAP参数，用户只需填写邮箱地址和授权码
        - **163邮箱：** 自动配置SMTP/IMAP参数，用户只需填写邮箱地址和授权码
        - **其他邮箱：** 用户手动配置所有SMTP/IMAP参数
        - **Gmail：** 使用OAuth 2.0授权模式，支持添加多个Gmail账号
    - **配置指导功能：**
        - QQ邮箱和163邮箱显示配置指导卡片，提供获取授权码的跳转链接
        - 提供连接测试功能验证配置正确性
    - **邮箱管理：**
        - 支持设置主要发件邮箱
        - 支持启用/禁用特定邮箱
        - 支持删除不需要的邮箱配置
- **2.1.3 登出:**
    - 用户可通过账户设置页面或用户菜单头像旁的设置按钮登出。
    - 登出后清除用户会话/Token，返回主界面。

### 2.2 核心界面 (UI)

- **2.2.1 整体布局:**
    - 采用左侧固定侧边栏 + 右侧主内容区的布局。
    - 主内容区顶部包含固定导航栏（Header）。
- **2.2.2 侧边栏 (Sidebar):**
    - **Logo区域:** 显示应用Logo和名称“跨境运营助手”。
    - **主菜单:**
        - **仪表盘 (Dashboard):** 默认首页，显示核心指标概览。
        - **产品库 (Product Library):** 管理产品信息。显示待处理项角标（如有）。
        - **AI助手 (AI Assistant):** 核心功能，进行产品分析和创作者匹配。
        - **建联记录 (Outreach Records):** 管理网红合作流程。显示待处理项角标（如有）。
        - 菜单项应高亮显示当前活动页面。
    - **用户信息区域:**
        - 显示当前登录用户的头像、用户名、邮箱。
        - 包含一个设置图标，点击可唤出菜单，包括登出和打开账户设置页面。
- **2.2.3 顶部导航栏 (Header):**
    - **页面标题:** 动态显示当前所在模块的名称（如 "AI助手", "产品库"）。
    - **~~全局搜索框:** 允许用户在应用内进行搜索。~~
    - **用户操作区:**
        - 通知图标：点击可展开通知列表
            - 此处展示最新的达人回信，点击跳转到对应的达人窗口
            - 查看全部通知，则链接到近期活动-查看全部中
            - 有角标数量提示数量
        - 用户头像：点击可展开用户菜单，同左下角设置图标。

### 2.3 仪表盘 (Dashboard)

- **2.3.1 KPI概览卡片:**
    - **活跃合作 (Active Collaborations):** 定义为“状态为‘沟通中’、‘已确认’或‘推广中’的建联记录数量”。
    - **本周新增建联 (New Outreach This Week):** 统计本周（周一至周日）内新创建的建联记录数量。
    - **活动触达潜力 (Potential Reach):** 实时计算所有状态为“推广中”的建联记录关联的创作者的订阅者数量总和。
    - **平均回复率 (Average Reply Rate):** 计算（状态为“沟通中”及后续状态的建联数 / 状态为“已联系”及后续状态的建联数） * 100%。
    - **趋势百分比:** 每个KPI卡片显示与上周同期数据的对比百分比（正数绿色向上箭头，负数红色向下箭头）。
    - **[后台需求]** 需要定时或实时计算这些指标，看性能是否能满足进入时刷新。
- **2.3.2 近期活动 (Recent Activities):**
    - 以时间线形式展示最近的应用内重要事件。
    - **事件类型:**
        - 邮件回复（需邮件集成）：如“Two Minute Papers 回复了你的邮件”。
        - 状态更新：如“MattVidPro AI 的合作状态更新为「已确认」”。
        - AI通知：如“AI助手推荐了3位新的合适博主”。
        - 操作记录：如“你向 AI TV 发送了合作邮件”。
    - 显示事件描述和发生时间（如“42分钟前”，“昨天 14:28”）。
    - 提供“查看全部”按钮，链接到更详细的活动日志页面（待设计）。
    - **[后台需求]** 记录用户操作和系统事件，并能根据用户过滤。
- **2.3.3 通知中心：**
    - 存在一个通知中心，通过右上角的通知图标，以及主页的最近活动进入
    - 用户查看过去的操作log
    - 通知中心有两个部分，一个用户主动操作的 log 记录时间线，一个是达人回复邮件的意图展示
- **2.3.4 合作漏斗 (Collaboration Funnel):**
    - 可视化展示建联流程中各个阶段的数量。
    - **阶段定义:**
        - **已联系 (Contacted):** 已通过平台发送首次建联邮件/消息的创作者数量。
        - **沟通中 (Communicating):** 收到创作者回复的建联数量。
        - **已确认 (Confirmed):** 用户手动标记为已达成合作意向的建联数量
        - **推广中 (Promoting):** 用户手动标记或系统（基于链接/邮件内容）识别为正在进行推广活动的建联数量。
    - 提供时间范围筛选器（如：本月、上月、本季度、全年）。
    - 显示每个阶段的数量和相对于上一阶段的转化率（可选）。
    - 显示总转化率（推广中 / 已联系）和平均合作周期（从已联系到推广中的平均天数）。
    - **[后台需求]** 基于建联记录的状态和时间戳进行统计。

### 2.4 产品库 (Product Library)

- **2.4.1 产品列表视图:**
    - 默认以网格（卡片）形式展示产品。
- **2.4.2 产品卡片:**
    - 显示产品图片。
    - 显示产品名称。
    - 显示产品分类标签（如：电子产品、价格亚马逊货币显示）。
    - 显示产品核心描述（限制ai 总结行数）。
    - 提供操作按钮：编辑、更多（包含删除等）。
- **2.4.3 添加/编辑产品:**
    - 提供“添加产品”按钮，等同于跳转到 ai 助手界面
- **2.4.4 搜索与筛选:**
    - 提供搜索框，支持模糊搜索产品名称、类别、描述关键词搜索。
    - 提供筛选控件（当前显示为标签和下拉按钮），支持按分类、价格区间等进行筛选。
    - 显示当前筛选条件和结果数量。
- **2.4.5 排序:**
    - 提供排序下拉菜单，支持按最新添加、价格（低到高/高到低）、受欢迎程度（待定义）排序。

### 2.5 AI助手 (AI Assistant)

- **2.5.1 界面布局:**
    - 初始页面：类似ai 助手页面放入商品链接
    - **AI助手信息区:** 显示AI助手头像、名称、当前关联产品。
    - **聊天容器:** 显示用户与AI的对话消息流。
    - **输入区域:** 文本输入框、附加操作按钮（链接、附件）、发送按钮。
- **2.5.2 核心交互流程:**
    - 一个产品对应一个 ai 对话，类似 chatgpt侧边可以切换商品的对应ai，包装感受上是每个商品都有对应专属 ai。
        - 对话过程并不是真的跟 ai对话，有固定的流程，模拟 ai 对话/先做成固定流程的体验
    
    **输入产品信息:**
    
    - 用户可在输入框粘贴产品URL或输入产品描述。
        - **链接识别:** 系统自动识别输入的URL类型：
            - **亚马逊链接:** 直接调用现有的亚马逊页面爬虫算法
            - **非亚马逊链接:** 调用通用爬虫接口获取页面数据
    - 点击发送按钮或按Enter键提交。
    
    **AI分析产品:**
    
    - **亚马逊商品分析流程:**
        - AI显示正在分析的消息。
        - 展示分析步骤（抓取链接、提取特征、匹配算法）。
        - 展示分析结果卡片，包含可编辑的产品名称、价格、核心特性、目标受众。
        - 用户可选择修改信息或直接确认开始匹配。
    - **非亚马逊商品分析流程:**
        - AI显示正在分析的消息。
        - **数据处理步骤:**
            - 调用通用爬虫接口获取页面原始数据
            - 使用数据清洗脚本处理爬虫返回的数据
            - 将清洗后的数据提交给LLM算法提取商品信息
        - **信息完整性检查:**
            - 系统检查提取的商品信息是否包含以下必需字段：
                - 商品名称
                - 商品价格
                - 商品特点
                - 商品类别
                - 详细描述
                - 品牌名称
        - **完整信息处理:**
            - 如果信息完整，直接展示分析结果卡片，流程同亚马逊商品
        - **不完整信息处理:**
            - 如果信息不完整，展示**信息补全卡片**：
                - 显示已提取的信息字段
                - 高亮标示缺失的必需字段
                - 提供可编辑的输入框供用户补全信息
                - 显示"完成补全"按钮
            - 用户补全信息后点击"完成补全"：
                - 将完整信息重新提交给算法进行商品标签提取
                - 展示最终的分析结果卡片
        - 用户可选择修改信息或直接确认开始匹配。
    - **AI匹配创作者:**
        - AI显示正在匹配的消息。
        - 匹配完成后，显示匹配到的创作者列表。
    - **营销活动设置:**
        - **触发时机:** 用户确认商品分析结果后，系统自动展示营销设置卡片
        - **设置界面:**
            - 展示**营销活动设置卡片**，包含两个必填项：
                - **营销总预算 (单位美金):**
                    - 输入框，支持数字输入。默认 0
                    - 建议范围提示：1$ - 100,000$
                - **预期建联数量:**
                    - 输入框，进行数据校验，只可填写 5 到 50 个
                    - 可选范围：5-50个，默认自动填写 10 个
                    - 说明文字："系统将为您匹配相应数量的优质达人"
                - **后台处理逻辑:**
                    - **数据获取规则:** 根据用户填写的数量X，系统从达人数据库获取 X * 2倍数量的达人信息 给到匹配算法
                    - **数据传递:** 将候选池中的所有达人信息传递给商品与达人匹配算法
                    - **最终输出:** 匹配算法分析候选池后，按匹配度排序并严格返回用户设置的X个达人
        - **用户交互:**
            - 用户填写完成后点击"开始匹配达人"按钮
            - 系统验证输入有效性（预算 > 0，建联数量在5-50 个）
            - 验证通过后进入"AI匹配创作者"步骤
        - **数据存储:**
            - 营销总预算存储到商品信息表的 `marketing_budget` 字段
            - 预期建联数量传递给匹配算法，控制返回的达人数量
    - **展示推荐创作者:**
        - 以卡片列表形式展示推荐的创作者。
        - **创作者卡片内容:** 头像、名称（含认证标识）、核心数据（订阅数、观看数、视频数）、相关度评分、详细匹配理由（基于产品特性和创作者内容）、AI沟通建议、操作按钮（添加到建联记录、查看详情）。
        - **[后台需求]** 需要创作者数据库（包含频道信息、内容标签、历史合作、受众画像等）。实现匹配算法，计算相关度并生成推荐理由和建议。数据来源可能是爬虫、第三方API或合作伙伴。
        - 提供JSON视图，展示更详细的后台数据（联系方式、预估成本等）。
    - **生成建联邮件 :**
        - （在原型后续流程或直接在聊天中）用户选择一个或多个推荐的创作者。
        - 用户请求生成建联邮件。
        - AI显示正在生成的消息。
        - 展示邮件预览卡片，包含收件人（选中的创作者）、可编辑的主题、AI生成的个性化邮件正文。
        - **[后台需求]** 实现基于产品信息、创作者特点和预设模板的个性化邮件生成（可用GPT等大模型）。
    - **发送邮件:**
        - 用户点击发送。
        - AI显示发送中及发送成功消息。
        - 提示用户可在“建联记录”中跟进。
        - **[后台需求]** 调用邮件API（如Gmail API）发送邮件，并将记录添加到建联记录。
- **2.5.3 第一次建联后:**
    
    当用户为某个商品发送了第一批建联邮件后，该商品的AI对话窗口将转变为以下管理模式：
    
    **界面核心元素:**
    
    - **当前活动商品:** 始终清晰标示当前对话是围绕哪个商品进行的。
    - **已联系博主列表:** 显示本次活动已发送邮件的博主卡片。
    - **核心功能按钮:** 固定的几个功能按钮。
    - **AI 消息推送区域:** AI 主动发送提醒和分析结果的地方。
    
    **具体功能流程:**
    
    **功能一：查找更多相似博主**
    
    - **触发:** 用户在AI对话界面点击固定按钮 `查找更多博主`。
    - **AI响应:**
        1. AI显示：“正在根据 [商品名称] 的特性为您寻找更多合适的YouTube博主...”
        2. AI重新运行匹配算法（可能排除已联系过的博主）。
        3. AI展示新一批推荐博主的卡片列表，信息同首次匹配（头像、名称、粉丝数、匹配度、匹配理由等）。
    - **用户交互:**
        1. 用户在新列表中勾选想要联系的博主。
        2. 点击 `生成建联邮件` 按钮。
        3. 流程回到之前的“邮件生成与预览”步骤 (原型图5)，之后发送邮件 (原型图6)。
        4. 新联系的博主自动加入到“已联系博主列表”中。
    - **价值:** 无需重复输入商品信息，快速扩大潜在合作博主范围。
    
    **功能二：建联状态追踪与接管提醒**
    
    - **状态展示与手动更新:**
        1. AI对话界面清晰展示“已联系博主列表”，每位博主卡片上显示**当前状态**（默认为“已发送”）。
        2. 每个博主卡片旁提供**状态更新按钮组**，例如：
            - `标记为已回复`
            - `标记为无意向`
            - `标记为洽谈中`
            - `标记为已合作`
        3. **用户操作:** 用户在自己的邮箱或其他渠道得知博主回复后，回到此界面，点击对应博主的按钮，手动更新状态。AI记录该状态。
    - **邮件回复功能:**
        1. **触发:** 当达人回复邮件时，ai 意图显示到
        2. **AI响应:** 立刻在消息区域推送一条**高亮提醒**：
        
        > 🔔 跟进提醒： 检测到 [博主名称] 已回复！建议您立即前往“达人建联页”进行沟通管理，把握合作机会。
        > 
        1. **交互:** 该提醒下方提供一个明确的按钮：
            - `前往接管建联 ([博主名称])`
    - **跳转至建联页:**
        1. 用户点击 `前往接管建联` 按钮。
        2. 系统**自动跳转**到建联记录模块
        3. 该页面应**自动聚焦**到刚刚标记为已回复的这位 **[博主名称]** 以及关联的 **[商品名称]** 的沟通记录上，方便用户查看历史邮件、记录沟通要点、安排下一步行动等。
    - **价值:** 将AI助手的“发现与触达”功能与实际的“沟通与管理”流程无缝衔接。虽然状态更新依赖手动，但AI的提醒和一键跳转极大提升了响应效率，解决了用户“知道回复了，然后去哪里处理”的问题。
    
    **功能三：建联效果分析**
    
    - **触发:** 用户点击AI对话界面的固定按钮 `查看效果分析`。
    - **AI响应:** AI基于当前用户为该商品**手动标记的所有博主状态**，实时计算并展示分析结果：
    
    > [商品名称] - 建联效果分析 (截至 [当前时间])已联系博主总数: [N] 位已回复博主数: [M] 位 (状态为“已回复”、“洽谈中”、“已合作”的总和)当前回复率: [M/N * 100]%初步意向率: [P] 位 (状态为“洽谈中”、“已合作”的总和) / [N] 位 * 100%无意向反馈: [Q] 位 (状态为“无意向”的数量)
    > 
    - **用户交互:** 结果以文本形式清晰展示在对话界面中。下方可提供按钮 `关闭分析` 或让其自然留在对话流中。
    - **价值:** 为用户提供针对单一商品营销活动的快速、量化的效果反馈，帮助判断策略是否有效，是否需要调整（例如，是否要“查找更多博主”或优化邮件模板）。

### 2.6 建联记录 (Outreach Records)

- **2.6 建联记录 (Outreach Records)**
    - **2.6.1 列表视图:**
        - 列定义: 创作者（头像、名称、核心数据）、关联产品（名称、分类）、当前状态、日期（上次联系、创建日期）、操作
        - 状态标签: 使用不同颜色和图标清晰标识建联状态（沟通中、已确认等）
    - **2.6.2 筛选与搜索:**
        - 提供状态标签页（全部、沟通中、已确认、推广中、已结束）进行快速筛选
        - 提供搜索框，支持按创作者名称、产品、状态关键词搜索
        - 提供按产品筛选的功能
    - **2.6.3 建联详情页面:**
        - 点击列表项（非按钮区域）时，跳转到独立的详情页面
        - **顶部达人信息区域:**
            - **左侧达人卡片:**
                - 显示达人头像、名称（含认证标识）
                - 关键数据（订阅数、总观看数）
                - 达人标签：显示达人的内容标签（如：科技达人、AI专家、产品测评）
            - **右侧产品信息:**
                - 显示关联的产品图片、名称
                - 简要描述、价格区间
        - **中部状态与操作栏:**
            - 时间信息: 显示上次发布时间（如"24h"）、上次沟通时间（如"2天前"）
            - 外部链接: "访问频道"按钮，直接跳转达人主页
            - 状态管理: 当前状态显示（如"沟通中"）带下拉菜单可修改状态
        - **内容标签页:**
            - 沟通记录标签页: 默认选中，显示所有沟通时间线
            - 近期内容标签页: 展示该创作者最近在各平台发布的内容
    - **2.6.4 沟通时间线 (沟通记录标签页):**
        - **基本布局:**
            - 按时间倒序排列，清晰的时间线布局
            - 左侧图标区分不同类型的记录
        - **邮件记录类型:**
            - **邮件回复（自动同步）:**
                - 显示达人主动回复的邮件
                - 自动从邮箱同步获取
            - **合作意向邮件（自动同步）:**
                - 显示系统发送的建联邮件
                - 记录发送状态和时间
            - **手动记录:**
                - 用户手动添加的沟通记录
                - 支持多种记录类型（邮件、通话、聊天、备注）
        - **邮件条目内容:**
            - 邮件类型标题（如"邮件回复（自动同步）"）
            - **邮件元信息:**
                - 发件人邮箱和时间戳（如"来自: <EMAIL> • 2023/03/21 14:30"）
            - **翻译功能:**
                - 蓝色翻译按钮
                - 点击后在邮件原文下方显示中文翻译
                - **[后台需求]** 需要集成翻译算法
            - **操作按钮:**
                - "查看邮件原文"按钮 - 查看完整邮件内容
                - "回复"按钮 - 快速回复该邮件
    - **2.6.6 发送邮件功能:**
        - **触发方式:**
            - 在沟通记录页面底部直接发送新邮件
            - 通过"回复"按钮回复特定邮件
        - **发送邮件界面:**
            - **收件人字段:**
                - 自动填充达人邮箱地址
                - 支持手动编辑（如需要抄送）
            - **邮件主题:**
                - 可编辑输入框
                - 回复时自动添加"Re:"前缀
                - 支持占位符提示
            - **邮件内容:**
                - 大文本输入框，支持富文本编辑
                - 支持插入链接、格式化文本
                - 回复时可引用原邮件内容
            - **发送按钮:**
                - 完成邮件发送
                - 自动记录到沟通时间线
                - **[后台需求]** 需要集成Gmail API或SMTP服务发送邮件
        - **邮件发送后处理:**
            - 自动在时间线中添加"已发送邮件"记录
            - 更新"上次联系时间"
            - 如果是首次发送，状态自动更改为"已联系"
    - 展示创作者在YouTube、Instagram等平台的近期帖子
    - 显示内容：缩略图、标题、发布信息、互动数据
    - 提供"加载更多"功能
    - **[后台需求]** 需要集成YouTube Data API、Instagram Graph API等
    - **2.6.6 状态管理:**
        - **状态流转规则:**
            - 未联系 → 已联系（发送首次邮件）
            - 已联系 → 沟通中（收到回复）
            - 沟通中 → 已确认（达成合作意向）
            - 已确认 → 推广中（开始推广活动）
            - 推广中 → 已完成（推广活动结束）
            - 任何状态 → 已拒绝（达人拒绝合作）
        - **状态修改:**
            - 用户可在详情页通过下拉菜单手动修改状态
            - 系统根据邮件内容智能建议状态更新
            - 状态变更自动记录到时间线
    - **2.6.7 数据同步需求:**
        - 邮件自动同步: 定期同步Gmail收件箱，识别达人回复
            - 目前是 5 分钟
        - 通知推送: 收到新回复时及时通知用户
        - 数据备份: 所有沟通记录需要可靠备份和导出功能
    - **2.6.8 邮件意图识别:**
        - **功能概述:** 对网红回复邮件进行智能分析，提取合作意图和项目进展信息
        - **Scene（邮件场景）分类:**
            - 积极合作: 明确表达合作兴趣
            - 探索沟通: 有兴趣但需更多信息
            - 报价回复: 提供具体价格条件
            - 确认合作: 同意条件，准备执行
            - 样品确认: 确认收到产品样品
            - 内容完成: 视频/内容制作完成
            - 婉拒合作: 明确或委婉拒绝
            - 自动回复: 非本人真实回复
            - 未知: 意图不明确或异常
        - **Stage（项目阶段）流程:**
            - 初次联系 → 意图探索 → 价格谈判 → 合作确认 → 样品发送 → 内容创作 → 内容审核 → 项目完成
            - 项目阶段只能向前推进，不可回退
            - 当邮件意图为"内容完成"且场景是"内容审核"时，显示专属图标【审核完成】，更新阶段为"项目完成"
        - **Summary（邮件摘要）:**
            - 30-50字自然语言概括邮件要点和建议下一步操作
            - 显示在邮件时间线中，便于用户快速了解邮件内容
        - **[后台需求]** 需要实现LLM算法分析邮件内容，识别意图并更新项目状态
    - **2.6.9 邮件内容生成:**
        - **功能概述:** 提供统一的AI邮件生成服务，根据合作阶段、邮件类型和上下文信息自动生成英文商务邮件
        - **邮件类型:**
            - first_contact: 初次建联
            - quote_negotiation: 价格谈判
            - shipping_notice: 发货通知
            - script_suggestion: 脚本建议
            - other: 自定义邮件
        - **内容要求:**
            - 全英文输出，邮件正文 ≤ 220英文单词
            - 语调友好自然，避免模板化表达
            - 根据历史邮件避免重复内容
            - 引用对方邮件内容时，单次引用 ≤ 15英文单词
        - **[后台需求]** 需要实现基于LLM的邮件生成算法，结合上下文生成个性化邮件
    - **2.6.10 达人信息展示标签:**
        - **在建联详情页的达人卡片中显示以下AI生成的标签:**
            - **综合人设/风格 (overallPersonaAndStyle):** 概括该达人在所有平台的整体形象和风格（如"专业科技测评师"）
            - **主要受众画像 (mainAudience):** 整合所有平台分析结果，描述核心受众特征（如"以美国年轻男性为主（18-34岁）"）
            - **潜在合作品牌类型 (potentialBrandType):** 基于内容、风格和受众，推测适合合作的品牌品类列表
            - **达人总体评价 (influencerEval):** 综合评估达人的内容质量、互动表现、独特性和发展潜力

### 2.7 CPM计算与智能定价系统

- **2.7.1 达人报价体系构建:**
    - **视频数据采集:**
        - 数据范围：采集每位达人最新5个视频的播放量、互动量数据
        - 互动量数据：YouTube和Instagram：点赞+评论数；TikTok：点赞+评论+收藏
        - 更新频率：首次采集后，每周自动更新一次
        - 展示位置：在达人卡片和详情页显示预估报价区间
    - **报价计算规则:**
        - 计算公式：CPM（每千次展示成本）× 最近5个视频平均播放量 ÷ 1000
        - 报价展示：以价格区间形式展示（如：$500-$1500）
        - CPM值获取：根据行业、粉丝数范围、地区、平台四个字段进行取值
        - 数据展示规范：所有报价统一使用美元符号"$"，数值超过1000时使用"k"简写
        - 更新状态提示：报价数据显示更新时间，超过14天未更新的数据显示警告图标
    - **[后台需求]** 需要实现每周自动更新达人最新报价范围的算法和数据存储系统
- **2.7.2 标签体系增强:**
    - **达人标签优化:**
        - 新增/优化标签类型：行业标签、语言标签、地区标签、enhancetags标签
        - 所有博主都再过一遍enhancetags，增加标签数量，提高命中率
        - 展示方式：在达人卡片上以彩色标签形式展示
        - 支持的标签分类：美妆时尚/3C/游戏/户外/母婴/其他
        - 使用优化后的AI算法生成更精准的标签，输出25个字段的完整分析报告
    - **商品行业标签:**
        - AI分析商品时自动生成行业标签
        - 所有商品默认打上3个enhancetags，增加标签数量，提高命中率和筛选池大小
        - 标签展示：在商品分析结果卡片中显示
        - 可编辑：用户可手动修改或添加行业标签
        - 支持行业分类：美妆时尚/3C/游戏/户外/母婴/其他
    - **[后台需求]** 需要实现基于GPT-4.1模型的标签增强算法，避免GPT-4o幻觉问题
- **2.7.3 智能预算分配:**
    - **达人智能筛选:**
        - 触发时机：用户设置营销预算和建联数量后
        - 筛选逻辑：
            - 计算单个达人平均预算（总预算÷建联数量）
            - 从3个enhancetags的达人池子里获取达人
            - 筛选报价在平均预算±20%范围内的达人
            - 若数量不足，去掉预算标签按活跃度获取进行匹配
            - 按达人活跃度从高到低排序，获取算法上限的人数（目前50人）
        - 结果展示：显示"已为您匹配X位符合预算的达人"
    - **动态建联数量推荐:**
        - 基于行业平均成本的算法逻辑：
            - 美妆时尚：$800-2000（CPM 35-70）
            - 3C：$1000-2500（CPM 20-40）
            - 游戏：$600-1500（CPM 15-30）
            - 户外：$500-1200（CPM 25-50）
            - 母婴：$700-1800（CPM 30-60）
            - 其他：$500-1500（默认中间值）
        - 交互设计：用户输入营销总预算后，建联数量输入框下方动态显示推荐范围
        - 计算逻辑：根据预算金额和行业平均报价计算合理区间
        - 实时更新：预算金额变化时，推荐范围实时更新
    - **[后台需求]** 需要实现基于行业数据的智能推荐算法和动态预算分配系统
- **2.7.4 界面交互优化:**
    - **营销活动设置界面更新:**
        - 预算输入优化：输入框增加货币符号前缀"$"，输入时显示千位分隔符，下方显示参考文案
        - 动态建联数量推荐：根据预算和行业自动计算推荐范围
        - 填入不合理建联预算和数量时，动态切换为无预算匹配模式
    - **达人列表增强显示:**
        - 新增信息展示：预估报价区间、活跃度指标、行业标签
        - 筛选器更新：新增按报价区间筛选、按行业标签筛选、按活跃度排序
        - 预算匹配度展示：在推荐达人卡片上显示"预算匹配度"百分比
        - 超预算提醒：若达人报价超出平均预算20%，显示提示标签"超预算"
    - **[后台需求]** 需要实现界面交互逻辑和数据展示优化

### 2.8 达人数据更新系统

- **2.8.1 功能概述:**
    - 建立智能化的达人数据更新机制，通过活跃度分析、业务价值评估和优先级排序，实现资源的精准投入和数据的动态维护
    - 支持新达人的自动发现和匹配
- **2.8.2 优先级评分体系:**
    - **达人更新优先级分数:**
        - 计算公式：`总分 = 活跃状态分 + 业务价值分 + 额外加成分`
        - 每周更新一轮≥0分的达人，0分以下停止更新
    - **活跃状态评分（基于近30天发布数）:**
        - ≥5条：50分（超活跃）
        - 4条：40分（活跃）
        - 3条：30分（正常）
        - 2条：20分（低活跃）
        - 1条：10分（低活跃）
        - 0条：0分（不活跃）
    - **业务价值评分:**
        - 合作成功达人：50分（近30天建联记录状态="已完成"）
        - 有回复达人：30分（近30天有回复过邮件）
        - 用户发送过邮件：10分（近7天算法推荐过给用户）
        - 无互动达人：0分
        - 同一达人只取最高分，不累加
    - **额外加成规则:**
        - 近期活跃：+10分（近14天有作品发布）
        - 百万大号：+20分（粉丝数>100万）
        - 十万达人：+10分（粉丝数>10万且≤100万）
        - 可能休眠：-100分（连续30天无发布）
        - 所有符合条件的加成项目可以累加，单个达人总加成不超过+50分
- **2.8.3 更新策略:**
    - **分组更新策略:**
        - 高优先级（≥30分）：每周2次更新
        - 中优先级（10-29分）：每周1次更新
        - 低优先级（1-9分）：每2周1次更新
        - 暂停更新（0分）：停止自动更新
    - **更新排队机制:**
        - 周一 00:00 → 计算所有达人分数
        - 周一 01:00 → 按分数从高到低排序
        - 周一-周日 → 按优先级顺序执行更新
        - 下周一重新计算分数和排序
    - **数据更新内容:**
        - 必更新字段：粉丝数、关注数、简介、最新内容、联系方式、内容标签
        - 可选更新字段：历史数据、粉丝画像、合作记录
- **2.8.4 YouTube RSS数据获取:**
    - **RSS源发现机制:**
        - 通过频道ID获取：`https://www.youtube.com/feeds/videos.xml?channel_id={CHANNEL_ID}`
        - 通过用户名获取（旧版频道）：`https://www.youtube.com/feeds/videos.xml?user={USERNAME}`
    - **RSS数据解析流程:**
        - RSS源监控 → 定时轮询RSS feeds
        - 新视频检测 → 对比上次获取的video_id
        - 基础信息提取 → 标题、发布时间、缩略图、视频链接
        - 数据库存储 → 记录到内容库video_content表
        - 爬虫任务队列 → 添加详细信息补充任务
    - **爬虫补充数据机制:**
        - 调用YouTube API补充视频详细描述、统计数据、评论内容
        - 设置抓取延迟在发布后的3天，确保数据稳定性
    - **[后台需求]** 需要实现RSS轮询、数据解析、YouTube API集成
- **2.8.5 新达人发现机制:**
    - **竞品分析模式:**
        - 工作流程：用户提交商品 → AI提取关键词 → 搜索竞品合作达人 → 分析其合作网红 → 扩展相似达人
        - 关键词提取规则：
            - 商品名称 → 品类词 + 功能词
            - 商品描述 → 场景词 + 特性词
            - 商品标签 → 目标人群
        - 使用AI生成30个英文搜索标签，去重历史查过的标签
    - **热门标签爬虫:**
        - 通过YouTube/Instagram/TikTok趋势标签发现网红
        - 使用web search工具获取实时热门话题
        - 将热门标签与商品库进行智能匹配
    - **新增达人标签增强:**
        - 使用GPT-4.1模型（避免4o幻觉问题）
        - 根据同一位达人的多条视频信息，综合判断适合推广的商品分类
        - 从500+预定义商品分类中选择3个最合适的标签
        - 输出格式：JSON数组 `["分类1","分类2","分类3"]`
    - **[后台需求]** 需要实现AI标签生成、爬虫系统、标签数据库管理

### 2.9 账户设置 (User Settings)

- **2.9.1 界面:**
    - 以独立页面或全屏模态框形式展示。
    - 包含清晰的段落标题（主账号信息、身份验证、授权服务等）。
- **2.9.2 主账号信息:**
    - 显示用户头像、用户名、邮箱。
    - 提供更换头像功能（需实现图片上传和存储）。
    - 允许编辑用户名。
    - 邮箱（主登录邮箱）通常设为不可更改。
    - 显示账号类型（如：企业账号）和状态（如：已验证）。
- **2.9.3 身份验证:**
    - 列出当前用于登录的身份验证方式（如：Google账号）。
    - 显示关联的邮箱。
    - 提供“断开连接”选项（需考虑对登录的影响，可能需要有其他验证方式时才允许断开）。
- **2.9.4 授权服务 / 关联账号:**
    - 用于管理应用对外部服务（如Gmail）的访问授权。
    - 列出已授权的Gmail账号。
    - **对每个账号:**
        - 显示服务名称（Gmail）、关联邮箱。
        - 标识是否为“主要”发件邮箱。
        - 显示已授予的权限（如：发送邮件、读取邮件）。
        - 提供操作：设为主要、管理权限（跳转Google授权页？）、移除授权。
    - **添加新账号:**
        - 提供“添加新的Gmail账号”按钮。
        - 点击后启动Google OAuth流程，请求所需权限，并将新账号添加到列表。
    - **邮箱配置列表：**
        - 显示所有已配置的邮箱账号（QQ、163、其他、Gmail）
        - 按邮箱类型分组显示：Gmail授权账号、SMTP/IMAP配置账号
    - **邮箱类型选择界面：**
        - 提供4个邮箱类型选项卡片：QQ邮箱、163邮箱、其他邮箱、Gmail
        - 每个卡片显示邮箱图标、名称和配置说明
    - **QQ邮箱配置：**
        - 自动填充服务器参数（smtp.qq.com:465, imap.qq.com:993）
        - 显示配置指导卡片：登录QQ邮箱 → 设置→账户 → 开启IMAP/SMTP服务 → 获取授权码
        - 提供跳转链接：https://mail.qq.com/
        - 用户填写：邮箱地址、授权码、显示名称
    - **163邮箱配置：**
        - 自动填充服务器参数（smtp.163.com:465, imap.163.com:993）
        - 显示配置指导卡片：登录163邮箱 → 设置→POP3/SMTP/IMAP → 开启服务 → 获取授权码
        - 提供跳转链接：https://mail.163.com/
        - 用户填写：邮箱地址、授权码、显示名称
    - **其他邮箱配置：**
        - 用户手动填写所有参数：SMTP/IMAP服务器、端口、加密方式、用户名、密码
        - 提供常见邮箱服务商配置模板
        - 支持连接测试功能
    - **Gmail OAuth配置：**
        - 点击Gmail选项后转换为OAuth授权界面
        - 显示"授权Gmail账号"按钮，跳转Google OAuth授权页面
        - 授权成功后自动配置，支持多Gmail账号管理
        - 显示已授权账号列表，包含权限范围和状态
    - **邮箱管理功能：**
        - **对每个邮箱账号：**
            - 显示邮箱类型、地址、状态（正常/错误/禁用）
            - 标识是否为"主要"发件邮箱
            - 显示最后同步时间和连接状态
            - 提供操作：设为主要、测试连接、编辑配置、删除账号