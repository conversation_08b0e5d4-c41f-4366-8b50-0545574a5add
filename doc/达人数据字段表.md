### 数据需求表

#### 1. 账号总览 (顶层)

| 字段名 | 字段描述 | 数据来源 | 数据结构 | 计算方式 |
| :--- | :--- | :--- | :--- | :--- |
| `subscribers` | 粉丝数 | 外部API | `Number` | 直接获取 |
| `last_post_date` | 最近发布时间 | 内部数据库 | `Date` | 查询`contents`表中该达人最新的`publishDate` |
| `last_promo_time` | 最近推广时间 | 外部API | `Date` | 直接获取 |
| `composite_score` | 综合评分 | 内部数据库 | `Number` | 加权平均 (详见文末) |
| `collaboration_tendency` | 合作倾向 | 内部数据库 | `Number` | 标签计分 (详见文末) |

---

#### 2. 数据总览 (子页签)

| 字段名 | 字段描述 | 数据来源 | 数据结构 | 计算方式 |
| :--- | :--- | :--- | :--- | :--- |
| `avg_views_30d` | 近30天平均观看 | 外部API | `Number` | 直接获取 |
| `avg_engagement_30d` | 近30天平均互动 | 外部API | `Number` | 直接获取 |
| `content_count_30d` | 近30天内容数量 | 外部API | `Number` | 直接获取 |
| `views_per_follower_ratio` | 观看量/粉丝数 | 内部数据库 | `Number` (Float) | `avg_views_30d` / `subscribers` |
| `estimated_exposure` | 预计曝光量 | 外部API | `Number` | 直接获取 |
| `growth_trend_data` | 增长趋势数据 | 内部数据库 | `JSON` | 定时任务记录历史数据 (详见文末) |
| `score_details` | 评分各维度指标 | 内部数据库 | `JSON` | 分别计算各子项得分 |
| `collaboration_labels` | 合作行为标签 | 外部API | `Array<String>` | 直接获取 |
| `estimated_cpm` | 预估CPM | 内部数据库 | `Number` | 关联查询`cpm_benchmarks`表 |
| `estimated_video_price` | 视频植入价格范围 | 外部API / 人工录入 | `String` | 直接获取 |
| `db_percentile_rank` | 平台粉丝数百分比排名 | 内部数据库 | `Number` | 百分位排名计算 |

---

#### 3. 受众数据 (子页签)

| 字段名 | 字段描述 | 数据来源 | 数据结构 | 计算方式 |
| :--- | :--- | :--- | :--- | :--- |
| `authenticity_score` | 粉丝可信度评分 | 外部API | `Number` (Float) | 直接获取 (e.g., 2.5/5) |
| `top_audience_country` | 最多受众国家 | 外部API | `String` | 直接获取 |
| `top_audience_gender` | 最多受众性别 | 外部API | `String` | 直接获取 |
| `top_audience_age_range` | 最多受众年龄段 | 外部API | `String` | 直接获取 |
| `geo_distribution` | 受众地理分布 | 外部API | `JSON` / `Array<Object>` <br> `[{ "country": "美国", "percentage": 21.1 }, ...]` | 直接获取 |
| `language_distribution` | 受众语言分布 | 外部API | `JSON` / `Array<Object>` <br> `[{ "language": "英语", "percentage": 52.9 }, ...]` | 直接获取 |
| `audience_demographics` | 受众人口统计特征 | 外部API | `JSON` <br> `{ "overall_gender_ratio": ..., "age_distribution": [...] }` | 直接获取 |
| `positive_feedback_ratio` | 正向反馈受众比例 | 外部API | `Number` (Float) | 直接获取 |
| `promo_interest_ratio` | 推广内容感兴趣比例 | 外部API | `Number` (Float) | 直接获取 |
| `promo_attraction_score` | 推广吸引度 | 外部API | `Number` (Integer) | 直接获取 (1-5星) |
| `promo_professional_score`| 推广专业度 | 外部API | `Number` (Integer) | 直接获取 (1-5星) |
| `interest_distribution` | 粉丝兴趣分布 | 外部API | `JSON` / `Array<Object>` <br> `[{ "interest": "彩妆", "percentage": 60.0, ... }]` | 直接获取 |
| `follower_authenticity` | 粉丝可信度分析 | 外部API / 内部计算 | `JSON` <br> `{ "type_distribution": ..., "real_follower_ratio": ... }` | `real_follower_ratio` = `ordinary` + `influencer` |
| `similar_influencers` | 相似受众网红 | 外部API | `JSON` / `Array<Object>` <br> `[{ "name": "Influencer B", ... }]` | 直接获取 (如果API提供) |
| `viewership_raw_hourly` | **[基础数据]** 原始小时观看强度 | 外部API | `JSON` <br> `{"monday": {"00": 15, ...}, ...}` | 直接获取 (7x24=168个数据点) |
| `viewership_by_day` | **[派生数据]** 观看量日期分布 | 内部计算 | `JSON` <br> `{"monday": 10.3, ...}` | 基于`viewership_raw_hourly`计算每日总和，再除以周总和得出百分比 |
| `viewership_by_timeslot` | **[派生数据]** 观看量时段分布 | 内部计算 | `JSON` <br> `{"0-3点": 8.5, ...}` | 基于`viewership_raw_hourly`按时段聚合数据，再计算百分比 |
| `peak_viewership_time` | **[派生数据]** 最佳发布时间 | 内部计算 | `String` | 遍历`viewership_raw_hourly`的168个数据点，找出值最大的键（星期+小时） |

---

#### 4. 内容数据 (子页签)

| 字段名 | 字段描述 | 数据来源 | 数据结构 | 计算方式 |
| :--- | :--- | :--- | :--- | :--- |
| `engagement_rate_30d` | 互动率 | 外部API | `Number` (Float) | 直接获取 |
| `views_per_follower_ratio`| 观看/粉丝数 | 外部API | `Number` (Float) | 直接获取 |
| `likes_per_view_30d` | 点赞/观看 | 外部API | `Number` (Float) | 直接获取 |
| `comments_per_view_30d` | 评论/观看 | 外部API | `Number` (Float) | 直接获取 |
| `shares_per_view_30d` | 分享/观看 | 外部API | `Number` (Float) | 直接获取 |
| `interaction_trend_data` | 互动-图表 | 外部API | `JSON` / `Array<Object>` <br> `[{ "date": "2025-06-24", "likes": 5000, ... }]` | 直接获取 |
| `publish_calendar` | 发布分析-发布日历 | 外部API | `JSON` / `Array<String>` <br> `["2025-07-01", ...]` | 直接获取 |
| `publish_frequency_week` | 发布分析-周发布频率 | 外部API | `JSON` <br> `{"monday": 1, ...}` | 直接获取 |
| `influencer_category` | 网红类别 | 外部API | `Array<String>` | 直接获取 |
| `top_hashtags` | Top5主题标签及词云 | 外部API | `JSON` / `Array<Object>` <br> `[{ "tag": "#barbara", "percentage": 11.4 }]` | 直接获取 |
| `total_archived_videos` | 已收录作品数 | 外部API | `Number` (Integer) | 直接获取 |
| `total_archived_views` | 已收录作品总观看 | 外部API | `Number` (Integer) | 直接获取 |
| `total_archived_likes` | 已收录作品总点赞 | 外部API | `Number` (Integer) | 直接获取 |
| `total_archived_comments` | 已收录作品总评论 | 外部API | `Number` (Integer) | 直接获取 |
| `content_list` | 内容数据墙(视频列表) | 外部API | `Array<Object>` <br> 每个Object包含: `video_id`, `title`, `cover_image_url`, `publish_date`, `duration_seconds`, `views`, `likes`, `comments`, `shares` | 直接获取 (分页加载) |

---

#### 5. 品牌数据 (子页签)

| 字段名 | 字段描述 | 数据来源 | 数据结构 | 计算方式 |
| :--- | :--- | :--- | :--- | :--- |
| `branded_ad_engagement` | 品牌广告效果 | 外部API | `Number` (Float) | 直接获取 |
| `branded_ad_frequency` | 发布品牌频次 | 外部API | `String` | 直接获取 |
| `branded_vs_non_branded_data` | 推广 VS 非推广 | 外部API | `JSON` <br> `{ "branded": { "avg_views": ... }, ... }` | 直接获取 |
| `brand_mentions` | 品牌提及 | 外部API | `JSON` / `Array<Object>` <br> `[{ "brand": "Victoria's Secret", ... }]` | 直接获取 |

---

### 6. 外部API数据需求 (最终版)

下表汇总了为实现产品功能，我们需要向外部API请求的**原始数据点**。我们倾向于在自己的后端进行指标计算，以保证灵活性和一致性。请贵方评估是否能提供以下数据。

#### A. 网红基础信息

| 字段名 | 字段描述 (我们需要什么) | 数据结构 & 示例 | 备注 (为什么需要以及如何使用) |
| :--- | :--- | :--- | :--- |
| `subscribers` | 网红在平台当前的**总粉丝数**。 | `Number` <br> `111500` | 用于展示在账号总览，并作为计算`观看量/粉丝数`等指标的分母。 |
| `influencer_category` | 对该网红内容领域的**分类标签**，可包含如“时尚”、“美妆”、“音乐”、“流行音乐”、“娱乐”、“电影”等多种类型。 | `Array<String>` <br> `["时尚", "美妆", "音乐", "娱乐"]` | 用于前端展示网红类别。 |

#### B. 核心分析数据

| 字段名 | 字段描述 (我们需要什么) | 数据结构 & 示例 | 备注 (为什么需要以及如何使用) |
| :--- | :--- | :--- | :--- |
| `follower_daily_snapshot` | **历史时间节点的粉丝总数快照**。 | `Array<Object>` <br> `[{ "date": "2025-07-18", "count": 111500 }, ...]` | **核心需求**。用于绘制“粉丝增长趋势”曲线。 |
| `views_daily_snapshot` | **历史时间节点的总观看量快照**。 | `Array<Object>` <br> `[{ "date": "2025-07-18", "count": 250000 }, ...]` | 用于绘制“观看量增长趋势”曲线，支撑内容表现分析。 |
| `video_count_daily_snapshot` | **历史时间节点的视频总量快照**。 | `Array<Object>` <br> `[{ "date": "2025-07-18", "count": 320 }, ...]` | 用于绘制“内容发布量增长趋势”曲线，分析发布活跃度。 |
| `geo_distribution` | 受众地理分布 | 外部API | `Array<Object>` <br> `[{ "country": "中国", "percentage": 35.2 }, ...]` | 直接获取 |
| `language_distribution` | 受众语言分布 | 外部API | `Array<Object>` <br> `[{ "language": "中文", "percentage": 60.1 }, ...]` | 直接获取 |
| `age_distribution` | 受众年龄分布 | 外部API | `Array<Object>` <br> `[{ "age_range": "18-24", "percentage": 40.5 }, ...]` | 直接获取 |
| `gender_distribution` | 受众性别分布 | 外部API | `Object` <br> `{ "male": 45.0, "female": 55.0 }` | 直接获取 |
| `positive_feedback_ratio` | 正向反馈受众比例 | 外部API | `Number` (Float) | 直接获取 |
| `promo_interest_ratio` | 推广内容感兴趣比例 | 外部API | `Number` (Float) | 直接获取 |
| `promo_attraction_score` | 推广吸引度 | 外部API | `Number` (Integer) | 直接获取 (1-5星) |
| `promo_professional_score`| 推广专业度 | 外部API | `Number` (Integer) | 直接获取 (1-5星) |
| `interest_distribution` | 粉丝兴趣分布 | 外部API | `Array<Object>` <br> `[{ "interest": "彩妆", "percentage": 60.0 }, ...]` | 直接获取 |
| `follower_type_distribution` | 粉丝类型分布（普通粉、可疑粉、僵尸粉、网红粉） | 外部API | `Object` <br> `{ "normal": 80.0, "suspicious": 11.9, "zombie": 5.0, "influencer": 3.1 }` <br> - `normal`：普通粉 <br> - `suspicious`：可疑粉 <br> - `zombie`：僵尸粉 <br> - `influencer`：网红粉 | 直接获取 |
| `viewership_raw_hourly` | **每小时的受众观看强度指数** (0-100)。 | `JSON` (7x24=168个数据点) <br> `{"monday": {"00": 15, ...}}` | **核心需求**。用于计算“观看量日期/时段分布”图表和“最佳发布时间”。 |
| `similar_influencers` | 与当前网红拥有**相似受众**的其他网红列表。 | `Array<Object>` (可选) <br> `[{ "name": "Influencer B", ...}]` | 如果提供，将直接用于“相似受众网红”模块的展示。 |
| `brand_mentions` | 近期内容中**提及的品牌列表**及相关信息。 | `Array<Object>` <br> `[{ "brand": "Victoria's Secret", ...}]` | 用于支撑「品牌数据」页签的展示。 |

#### C. 内容列表 (最核心的数据源)

我们需要一个接口，能返回指定网红**最近一段时间（如90天内）发布的全部内容列表**。列表中的每个视频对象都需要包含以下字段：

| 字段名 | 字段描述 (我们需要什么) | 数据结构 & 示例 | 备注 (为什么需要以及如何使用) |
| :--- | :--- | :--- | :--- |
| `video_id` | 视频的唯一ID。 | `String` | 用于唯一标识和关联。 |
| `title` | 视频标题。 | `String` | 展示，并可用于LLM分析。 |
| `description` | 视频简介。 | `String` | 展示，并可用于LLM分析。 |
| `publish_date` | 视频的发布日期。 | `Date` | 用于计算`last_post_date`，并筛选30天内内容。 |
| `views` | 该视频的**总观看量**。 | `Number` | 用于计算`avg_views_30d`等指标。 |
| `likes` | 该视频的**总点赞数**。 | `Number` | 用于计算互动量。 |
| `comments` | 该视频的**总评论数**。 | `Number` | 用于计算互动量。 |
| `shares` | 该视频的**总分享数**。 | `Number` | 用于计算互动量。 |
| `is_promoted` | **布尔值**，标记该视频是否为商业推广内容。 | `Boolean` <br> `true` / `false` | **核心需求**。用于计算`last_promo_time`和`branded_vs_non_branded_data`。 |
| `cover_image_url` | 视频封面图链接。 | `String` | 用于“内容数据墙”的展示。 |
| `duration_seconds` | 视频时长（秒）。 | `Number` | 用于“内容数据墙”的展示。 |
| `tags` | 视频内容标签（如主题、风格、话题等）。 | `Array<String>` <br> `["美妆", "测评"]` | 用于内容分类、筛选和分析。 |

---

### 关键模块实现逻辑详解 (基于API数据)

1.  **数据总览指标计算**: 
    *   `avg_views_30d`, `avg_engagement_30d`, `content_count_30d`等指标，将通过拉取近30天的「内容列表」，由我方后端进行循环累加和平均计算得出。
    *   `last_promo_time` 将通过筛选「内容列表」中 `is_promoted: true` 的视频，并找出最新的 `publish_date` 来确定。

2.  **增长趋势图**: 
    *   “粉丝增长”曲线依赖于 `follower_daily_snapshot`。
    *   “观看量增长”和“发布量增长”曲线，将通过每日定时拉取并存储「内容列表」数据，由我方进行历史数据聚合来生成。

3.  **综合评分与合作倾向**: 
    *   如 `provider_score_details` 不可用，我方将基于「内容列表」和「核心分析数据」自行建立评分模型。
    *   “合作倾向”将基于 `collaboration_labels` 在我方后端进行计分。