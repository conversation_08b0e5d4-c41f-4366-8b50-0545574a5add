# 1.1 引言

### 文档目的

本文档旨在详细说明跨境达人助手系统的架构设计、数据结构、接口定义以及功能实现，为开发团队和项目相关方提供系统实现的技术指导和参考。

# 1.2 系统概述

### 系统架构图

[](https://xcnjoit6imgy.feishu.cn/space/api/box/stream/download/asynccode/?code=ZDk4Mjk5OWFkMWJhYmY0ZDIzNDVhNDZmN2E1YzBkYmNfQkZZUlB5QjNYYzNrTU1KVXNHNU1vSWlkd3V2SXZBbzNfVG9rZW46UldPaWJBa0Fzb1BpT2R4YllqdGMxeTJPblBoXzE3NTI2NTk4MzE6MTc1MjY2MzQzMV9WNA)

### 系统功能简介

跨境达人助手是一个专为跨境电商企业设计的达人运营管理系统，主要功能包括：

1. **达人管理**：对多平台（YouTube、TikTok、Instagram 等）达人账号进行统一管理
2. **达人数据分析**：自动化收集和分析达人内容、互动率、受众画像等数据
3. **外联沟通**：管理与达人的沟通流程，包括联系、谈判和合作过程
4. **产品管理**：管理适合达人营销的产品信息
5. **数据仪表盘**：提供业务分析和决策支持的数据可视化功能

后台系统采用 Nuxt.js 前端框架和 Node.js 后端服务，搭配 MongoDB 数据库，实现了一个现代化的全栈 Web 应用。

# 1.3 环境配置

数据库：

| uri | 环境 |
| --- | --- |
| ************************************************************** | dev |

# 1.4 数据结构

## 数据结构定义

系统主要包含以下核心数据结构：

## 数据字典（Data Dictionary）

### 达人账号总表 （influencers）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 达人唯一标识，MongoDB 自动生成 |
| name | String | 100 | 是 | 达人名称，主平台名称 |
| mainLanguage | String | 50 | 是 | 主语言，如 " 英语 "、" 中文 " |
| region | String | 50 | 是 | 主要地区，如 " 美国 "、" 英国 " |
| associatedPlatforms | Array[String] | - | 是 | 关联平台，如 ["tiktok"，"ins"] |
| mainPlatform | String | 50 | 是 | 主要平台，数据最好的平台 |
| engagementEvaluation | Number | - | 是 | 互动程度评估，主要平台的互动率数据 |
| isActive | Boolean | - | 是 | 是否活跃，任一平台活跃即为 true |
| contactInfo | String | 100 | 是 | 联系方式，通常为邮箱地址 |
| coreContentDirection | Array[String] | - | 是 | 核心内容方向，如 [" 家电 "，" 美妆 "] |
| overallPersonaAndStyle | Array[String] | - | 是 | 综合人设/风格， [AI 生成] 概括该达人在所有平台的整体形象和风格 （如 "专业科技测评师"， "轻松幽默生活博主"）。 |
| mainAudience | Array[String] | - | 是 | 主要受众画像，**[AI 生成]** 整合所有平台分析结果，描述核心受众特征 （如 "以美国年轻男性为主（18-34 岁）"）。 |
| commercialDegree | String | - | 是 | 商业化程度评估，**[AI 生成]** 评估该达人整体的商业推广参与程度 （如 "中度商业化"， "高度商业化"）。 |
| crossPlatformConsist | String | - | 是 | 跨平台内容一致性，**[AI 生成]** 评估该达人在不同平台发布内容的相似度或差异化程度 （如 "高度一致"， "平台差异化明显"）。 |
| potentialBrandType | Array[String] | - | 是 | 潜在合作品牌类型，**[AI 生成]** 基于内容、风格和受众，推测适合合作的品牌品类列表 （如 ["数码产品"， "快时尚"， "生活用品"]）。 |
| influencerEval | String | - | 是 | 达人总体评价，**[AI 生成]**  "综合评估达人在该平台的内容质量、互动表现、独特性和发展潜力 （例如：内容质量高，互动积极，风格独特，潜力巨大 / 内容同质化，互动一般，有待提升 / 更新频率低，影响力有限 等）"， |
| goodsCarryRating | String | - | 是 | 带货能力评级，**[AI 生成]** 评估达人推广产品并促成转化的潜力等级 （例如：高， 中， 低， 无法判断）。评级依据包括：推广频率与自然度、产品与内容的契合度、受众对推广内容的反馈（评论/点赞情况）、内容垂类的购买属性等 |
| updatedAt |  |  |  |  |

### 平台账号详情表 （platforms）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 平台记录唯一标识 |
| influencerId | Number | - | 是 | 关联的达人 ID |
| influencerName | String | 100 | 是 | 达人在该平台的名称 |
| platform | Enum | - | 是 | 平台类型：youtube/tiktok/ins |
| profileUrl | String | 255 | 是 | 达人主页链接 |
| avatarUrl | String | 255 | 是 | 头像图片 URL |
| contactInfo | String | 100 | 否 | 平台提供的联系方式 |
| subscribers | Number | - | 是 | 订阅者/粉丝数 |
| totalViews | Number | - | 否 | 累计观看数（YouTube） |
| likes | Number | - | 否 | 获赞数（TikTok） |
| engagementRate | Number | - | 是 | 互动率，（点赞 + 评论 + 分享） |
| engagementTrend | Number | - | 是 | 互动率趋势，最新 3 条的互动率 |
| promotedProductCategory | Array[String] | - | 否 | 推广商品分类 |
| enhancedTags | Array[String] | - | 否 | 增强标签 |
| recentVideoPlays | Number | - | 是 | 近期视频播放量 |
| videoPublishFrequency | Number | - | 是 | 视频发布频率 |
| recordTime | Date | - | 是 | 爬虫入库时间 |
| lastUpdateTime | Date | - | 是 | 最后更新时间 |
| isActive | Boolean | - | 是 | 是否活跃，近 7 天是否有发布作品 |
| audienceGender | String | 50 | 否 | AI 分析的受众性别 |
| audienceAge | String | 50 | 否 | AI 分析的受众年龄 |
| regionCountry | String | 50 | 否 | AI 分析的地区/国家 |
| language | String | 50 | 否 | AI 分析的语言 |
| contentFormat | Array[String] | 50 | 否 | 内容格式 |
| recentContentSummary | String | 500 | 否 | AI 分析的近期内容总结 |
| videoStyle | String | 50 | 否 | AI 分析的视频风格 |
| contentTone | String | 50 | 否 | 内容调性 |
| categoryDepth | String | 50 | 否 | 垂类深度 |
| promotionAbility | String | 50 | 否 | AI 判断的推广能力评估 |
| brandRepetitionRate | String | 50 | 否 | 品牌重复率 |
| contentScene | Array[String] | 100 | 否 | 内容展示场景 |
| audienceCountry | String | 50 | 否 | AI分析的粉丝主要国家 |
| audienceLanguage | String | 50 | 否 | AI分析的粉丝主要语言 |
| audiencePurchasePower | String | 50 | 否 | AI分析的粉丝购买力 |
| industry | String | 50 | 否 | AI分析的行业分类 |
| cpmPrice |  |  |  |  |
| activityScore | Number | - | 是 | 活跃状态分（0-50分）：基于近30天发布数量的评分 |
| businessScore | Number | - | 是 | 业务价值分（0-50分）：基于合作状态和互动情况的评分 |
| bonusScore | Number | - | 是 | 额外加成分（-100 ~ +50分）：各种加成和减分项的总和 |
| priorityScore | Number | - | 是 | 更新优先级总分：activityScore + businessScore + bonusScore |
| **noxScore** | **Float** | **-** | **否** | **Nox综合评分(1-5分)** |
| **noxScoreDetails** | **JSON** | **-** | **否** | **雷达图各维度评分** |
| **collaborationTendency** | **Integer** | **-** | **否** | **合作倾向评分(1-10分)** |
| **collaborationLabels** | **Array[String]** | **-** | **否** | **合作相关标签** |
| **estimatedCPM** | **Float** | **-** | **否** | **预估CPM价格** |
| **estimatedVideoPrice** | **String** | **-** | **否** | **视频植入价格范围** |
| **globalRank** | **Integer** | **-** | **否** | **全球粉丝数排名** |
| **countryRank** | **Integer** | **-** | **否** | **所在国家粉丝数排名** |
| **lastPromoTime** | **Date** | **-** | **否** | **最近推广时间** |
| **avgViews30d** | **Integer** | **-** | **否** | **近30天平均观看量** |
| **avgEngagement30d** | **Integer** | **-** | **否** | **近30天平均互动量** |
| **contentCount30d** | **Integer** | **-** | **否** | **近30天内容数量** |
| **viewsPerFollowerRatio** | **Float** | **-** | **否** | **观看量/粉丝数比例** |
| **engagementRate30d** | **Float** | **-** | **否** | **近30天互动率** |
| **likesPerView30d** | **Float** | **-** | **否** | **点赞/观看比例** |
| **commentsPerView30d** | **Float** | **-** | **否** | **评论/观看比例** |
| **sharesPerView30d** | **Float** | **-** | **否** | **分享/观看比例** |
| **interactionTrendData** | **JSON** | **-** | **否** | **互动趋势图数据** |
| **publishCalendar** | **JSON** | **-** | **否** | **发布日历数据** |
| **publishFrequencyWeek** | **JSON** | **-** | **否** | **周度发布频率** |
| **topHashtags** | **JSON** | **-** | **否** | **Top标签及占比** |
| **totalArchivedVideos** | **Integer** | **-** | **否** | **已收录作品总数** |
| **totalArchivedViews** | **Integer** | **-** | **否** | **已收录作品总观看** |
| **totalArchivedLikes** | **Integer** | **-** | **否** | **已收录作品总点赞** |
| **totalArchivedComments** | **Integer** | **-** | **否** | **已收录作品总评论** |
| **authenticityScore** | **Float** | **-** | **否** | **粉丝可信度评分(1-5分)** |
| **realFollowerRatio** | **Float** | **-** | **否** | **真实粉丝比例** |
| **topAudienceCountry** | **String** | **50** | **否** | **最多受众国家** |
| **topAudienceGender** | **String** | **50** | **否** | **最多受众性别** |
| **topAudienceAge** | **String** | **50** | **否** | **最多受众年龄段** |
| **geoDistribution** | **JSON** | **-** | **否** | **受众地理分布** |
| **languageDistribution** | **JSON** | **-** | **否** | **受众语言分布** |
| **genderDistribution** | **JSON** | **-** | **否** | **性别分布** |
| **ageDistribution** | **JSON** | **-** | **否** | **年龄分布** |
| **positiveFeedbackRatio** | **Float** | **-** | **否** | **正向反馈受众比例** |
| **promoInterestRatio** | **Float** | **-** | **否** | **推广内容感兴趣比例** |
| **promoAttractionScore** | **Integer** | **-** | **否** | **推广吸引度评分(1-5分)** |
| **promoProfessionalScore** | **Integer** | **-** | **否** | **推广专业度评分(1-5分)** |
| **interestDistribution** | **JSON** | **-** | **否** | **粉丝兴趣分布** |
| **followerTypeDistribution** | **JSON** | **-** | **否** | **粉丝类型分布** |
| **brandedAdEngagement** | **Float** | **-** | **否** | **品牌广告互动率** |
| **brandedAdFrequency** | **String** | **50** | **否** | **品牌广告发布频次** |
| **brandedVsNonBrandedData** | **JSON** | **-** | **否** | **推广vs非推广数据对比** |
| **brandMentions** | **JSON** | **-** | **否** | **品牌提及分析结果** |
| **commercialContentRatio** | **Float** | **-** | **否** | **商业内容占比** |
| **brandDiversityScore** | **Float** | **-** | **否** | **品牌多样性评分** |
| **mostMentionedBrand** | **String** | **100** | **否** | **最常提及品牌** |
| **followerGrowthTrend** | **JSON** | **-** | **否** | **粉丝增长趋势数据** |
| **likesGrowthTrend** | **JSON** | **-** | **否** | **点赞增长趋势数据** |
| **videoGrowthTrend** | **JSON** | **-** | **否** | **视频增长趋势数据** |
| **engagementGrowthTrend** | **JSON** | **-** | **否** | **互动增长趋势数据** |
| **dataCompleteness** | **Float** | **-** | **否** | **数据完整度评分** |
| **lastAnalysisTime** | **Date** | **-** | **否** | **最后分析时间** |
| **nextUpdateTime** | **Date** | **-** | **否** | **下次更新时间** |
| **dataQualityScore** | **Float** | **-** | **否** | **数据质量评分** |

### 达人内容表 （contents）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 内容唯一标识 |
| influencerId | Number | - | 是 | 关联的达人 ID |
| platformId | Number | - | 是 | 关联的平台 ID |
| influencerName | String | 100 | 是 | 发布该内容的达人名称 |
| platform | Enum | - | 是 | 内容平台：youtube/tiktok/ins |
| title | String | 255 | 是 | 内容标题 |
| promotedProductCategory | Array[String] | - | 否 | 推广商品分类 |
| enhancedTags | Array[String] | - | 否 | 增强标签 |
| coverImage | String | 255 | 否 | 视频封面图 URL |
| videoUrl | String | 255 | 是 | 视频链接 |
| likes | Number | - | 是 | 点赞数 |
| comments | Number | - | 是 | 评论数 |
| publishDate | Date | - | 是 | 内容发布日期 |
| commentContent | String | 1000 | 否 | AI 识别的评论区内容 |
| **views** | **Number** | **-** | **是** | **播放量** |
| **description** | **String** | **1000** | **否** | **内容简介** |
| **topComments** | **Array[Object]** | **-** | **否** | **热门评论 (最多前三条，每条包含评论内容和点赞数)** |
| **contentType** | **Enum** | **-** | **是** | **内容类型 (例如: 视频、短视频、图文)** |

### 产品表 （products）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 产品唯一标识 |
| name | String | 100 | 是 | 产品名称 |
| image | String | 255 | 是 | 产品图片 URL |
| category | String | 50 | 是 | 产品分类 |
| price | String | 50 | 是 | 产品价格 |
| description | String | 1000 | 是 | 产品描述 |
| outreachCount | Number | - | 是 | 外联次数 |
| contactCount | Number | - | 是 | 联系次数 |
| cooperationCount | Number | - | 是 | 合作次数 |

### 外联记录 （outreach）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 外联记录唯一标识 |
| influencer | String | 100 | 是 | 外联对象 |
| status | Enum | - | 是 | 外联状态 |
| lastMessage | String | 1000 | 是 | 最后一条消息 |
| intent | String | 1000 | 是 | 意向 |
| budget | Number | - | 是 | 预算 |
| averageResponseTime | Number | - | 是 | 平均响应时间 |
| email | String | 100 | 是 | 外联对象邮箱 |
| emails | Array[OutreachEmail] | - | 是 | 外联邮件列表 |
| updatedAt | Date | - | 是 | 更新时间 |
| createdAt | Date | - | 是 | 创建时间 |

### 外联邮件 （outreach_emails）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 外联邮件唯一标识 |
| outreach | String |  | 是 | 外联记录id |
| subject | String | 100 | 是 | 邮件主题 |
| content | String | 1000 | 是 | 邮件内容 |
| date | String | 50 | 是 | 邮件日期 |
| direction | Enum | - | 是 | 邮件方向：sent/received |
| mode | Enum | - | 是 | 邮件模式：email/youtubeMsg/youtubeComment/tictokMsg/tictokComment/instagramMsg/instagramComment |
| email | Object | - | 是 | 邮箱信息 |
| remark | String | 1000 | 是 | 备注 |
| updatedAt | Date | - | 是 | 更新时间 |
| createdAt | Date | - | 是 | 创建时间 |

### AI 助手会话 （ai_chat_sessions）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | AI 助手会话唯一标识 |
| title | String | 100 | 是 | 会话标题 |
| systemPrompt | String | 1000 | 是 | 系统提示 |
| createdAt | Date | - | 是 | 创建时间 |
| updatedAt | Date | - | 是 | 更新时间 |
| messages | Array[AIChatMessage] | - | 是 | 消息列表 |

### AI 助手消息 （ai_chat_messages）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | AI 助手消息唯一标识 |
| aiChatSessionId | String | - | 是 | 关联的 AI 助手会话 ID |
| role | Enum | - | 是 | 消息角色：user/assistant |
| content | String | 1000 | 是 | 消息内容 |
| responseType | Enum | - | 是 | 回复类型：text/crawl/productAnalysis/influencerMatch/emailConfirm/emailSend |
| crawl | Object | - | 否 | 爬虫数据 |
| productAnalysis | Object | - | 否 | 商品分析数据 |
| influencerMatch | Object | - | 否 | 匹配博主数据 |
| emailConfirm | Object | - | 否 | 建联邮件数据 |
| emailSend | Object | - | 否 | 邮件发送数据 |
| createdAt | Date | - | 是 | 创建时间 |
| updatedAt | Date | - | 是 | 更新时间 |

### 用户认证 （users）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 用户唯一标识 |
| username | String | 100 | 是 | 用户名 |
| email | String | 100 | 是 | 邮箱 |
| passwordHash | String | 100 | 是 | 密码哈希值 |
| role | Enum | - | 是 | 用户角色：admin/user/manager |
| createdAt | Date | - | 是 | 创建时间 |
| lastLogin | Date | - | 否 | 最后登录时间 |
| status | Enum | - | 是 | 状态：normal/disabled |

### 外联统计 （outreach_stats）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 外联统计唯一标识 |
| all | Number | - | 是 | 总数 |
| pending | Number | - | 是 | 待回复数量 |
| replied | Number | - | 是 | 已回复数量 |
| collaborated | Number | - | 是 | 已合作数量 |
| rejected | Number | - | 是 | 已拒绝数量 |
| dayType | Enum | - | 是 | 时间类型：day/month/year |
| date | String | 50 | 是 | 日期 |

### 仪表盘统计 （dashboard_stats）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 仪表盘唯一标识 |
| totalProducts | Number | - | 是 | 产品总数 |
| totalInfluencers | Number | - | 是 | 达人总数 |
| activeInfluencers | Number | - | 是 | 活跃合作数 |
| successCollaborations | Number | - | 是 | 成功合作数 |
| activityPotential | Number | - | 是 | 活动触达潜力 |
| avgResponseTime | Number | - | 是 | 平均响应时间 |
| dateType | Enum | - | 是 | 时间类型：day/month/year |
| date | String | 50 | 是 | 日期 |

### 活动记录 （activities）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 活动记录唯一标识 |
| type | Enum | - | 是 | 活动类型：response/collaboration/content/system |
| description | String | 1000 | 是 | 活动描述 |
| influencerId | String | - | 否 | 关联的达人 ID |
| productId | String | - | 否 | 关联的产品 ID |
| createdAt | Date | - | 是 | 创建时间 |
| updatedAt | Date | - | 是 | 更新时间 |

### 品牌提及详情表（brand_mentions）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 记录唯一标识 |
| platformId | String | - | 是 | 关联的平台记录ID |
| influencerId | Number | - | 是 | 关联的达人ID |
| videoId | String | 100 | 是 | 相关视频ID |
| brandName | String | 100 | 是 | 品牌名称 |
| brandCategory | String | 50 | 否 | 品牌分类 |
| mentionType | Enum | - | 是 | 提及类型：explicit/implicit/product_placement |
| confidenceScore | Float | - | 是 | 置信度分数 |
| mentionContext | String | 500 | 否 | 提及上下文 |
| sentiment | Enum | - | 否 | 情感倾向：positive/neutral/negative |
| videoTitle | String | 255 | 否 | 视频标题 |
| publishDate | Date | - | 是 | 发布日期 |
| views | Integer | - | 否 | 视频观看量 |
| engagement | Integer | - | 否 | 视频互动量 |
| createdAt | Date | - | 是 | 分析时间 |

### 内容表现详情表（content_performance）

| 字段名 | 数据类型 | 长度 | 是否必填 | 描述 |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 记录唯一标识 |
| platformId | String | - | 是 | 关联的平台记录ID |
| videoId | String | 100 | 是 | 视频ID |
| videoTitle | String | 255 | 否 | 视频标题 |
| videoDescription | String | 1000 | 否 | 视频描述 |
| publishDate | Date | - | 是 | 发布日期 |
| views | Integer | - | 是 | 观看量 |
| likes | Integer | - | 是 | 点赞数 |
| comments | Integer | - | 是 | 评论数 |
| shares | Integer | - | 否 | 分享数 |
| duration | Integer | - | 否 | 视频时长(秒) |
| thumbnailUrl | String | 255 | 否 | 缩略图URL |
| hashtags | Array[String] | - | 否 | 标签列表 |
| isBranded | Boolean | - | 是 | 是否为品牌内容 |
| engagementRate | Float | - | 是 | 互动率 |
| performanceScore | Float | - | 是 | 表现评分 |

### 亚马逊产品（amazon_product）

| 字段名 | 数据类型 | 是否必填 | 描述 |  | 必要字段 |
| --- | --- | --- | --- | --- | --- |
| _id | String | 是 | 产品唯一标识 |  |  |
| product_title | String | 是 | 商品标题，描述了商品的主要信息 |  | 1 |
| price | String | 是 | 商品价格，单位为美元 |  | 1 |
| rating | Number | 是 | 商品评级 | 2.9 |  |
| review_count | Number | 是 | 商品的评论数量 |  |  |
| availability | String | 是 | 商品的库存情况描述 | 库存中仅剩 13 件 - 欲购从速。 |  |
| seller | String | 是 | 商品的卖家名称 | Amazon Export Sales LLC |  |
| seller_url |  | 是 | 卖家的网址链接 |  |  |
| seller_address | String | 是 | 卖家的地址，包含街道、城市、州和国家信息 |  |  |
| product_url | String | 是 | 商品的网址链接 |  |  |
| asin | String | 是 | 亚马逊标准识别号码，用于唯一标识商品 | B006UACRTG |  |
| image_url | String | 是 | 商品图片链接 |  |  |
| features | String | 是 | 商品的特点描述，列举了该商品的各项特性和功能 |  | 1 |
| description | String | 是 | 商品的详细描述，包括产品介绍、功能、系统要求、保修信息等多方面内容 |  | 1 |
| category_source | String | 是 | 商品的类别来源 | 配件和耗材 | 1 |
| brand_name | String | 是 | 商品的品牌名称 | datacolor | 1 |
| listing_date | String | 是 | 商品在平台上的上架日期 |  |  |