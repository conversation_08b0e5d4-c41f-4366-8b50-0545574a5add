# ***邮件意图识别功能需求规范***

## 功能概述

对网红回复邮件进行智能分析，提取合作意图和项目进展信息，为后续自动化流程提供决策依据。

1. 核心概念定义

### 2.1 Scene（邮件场景）

**定义**：当前这封邮件表达的主要合作意图

**作用**：标记在具体邮件记录上，用于邮件列表展示和意图分析

**生命周期**：每封邮件都有独立的scene标识

### 2.2 Stage（项目阶段）

**定义**：整个建联项目当前所处的标准流程节点

**作用**：记录在建联记录上，决定下一步可执行的操作

**更新机制**：每次收到新邮件后，根据分析结果自动更新项目阶段

### 2.3 Summary（邮件摘要）

**定义**：用自然语言概括邮件核心要点和建议下一步操作

**作用**：显示在邮件时间线中，便于用户快速了解邮件内容

**要求**：30-50字，语言自然，适合界面直接展示

## 输入数据规范

### 2.1 数据结构

```
{
  "thread_history": [
    {
      "timestamp": "2024-06-14 10:30:00",
      "subject": "Collaboration Opportunity",
      "summary": "发送合作邀请邮件，介绍产品和合作意向"
      "scene":"积极探索"
    },
    {
      "timestamp": "2024-06-15 14:20:00",
      "subject": "Re: Collaboration Opportunity",
      "summary": "网红表达初步兴趣，询问更多产品信息"
      "scene":"样品"
    }
  ],
  "current_email": {
    "subject": "Re: Collaboration Opportunity",
    "content": "Thanks for the details! My rate for this type of collaboration is $800. Let me know if this works for you.",
    "timestamp": "2024-06-16 09:15:00"
  },
  "influencer_profile": {
    "name": "Alex Tech Review",
    "category": "科技测评",
    "audience": "年轻男性为主，对科技产品感兴趣",
    "style": "专业测评，注重产品细节"
  },
  "product_info": {
    "name": "智能运动耳机",
    "category": "电子产品",
    "price": "$199",
    "target_audience": "运动爱好者，年轻用户群体"
  },
  "current_project_stage": "意图探索"
}

```

### 2.2 字段说明

- **thread_history**: 历史邮件摘要列表，按时间正序排列
- **current_email**: 当前需要分析的邮件完整内容
- **influencer_profile**: 网红档案信息（可选）
- **product_info**: 产品基本信息（可选）
- **current_project_stage**: 系统记录的当前项目阶段（可选）
1. 输出规范

### 2.3 Scene分类标准

| **分类值** | **含义** | **典型表现** |
| --- | --- | --- |
| 积极合作 | 明确表达合作兴趣 | "I'm interested", "Let's do this" |
| 探索沟通 | 有兴趣但需更多信息 | "Tell me more", "What's included?" |
| 报价回复 | 提供具体价格条件 | "My rate is $500", "I charge..." |
| 确认合作 | 同意条件，准备执行 | "Sounds good", "Here's my address" |
| 样品确认 | 确认收到产品样品 | "Package received", "Got the product" |
| 内容完成 | 视频/内容制作完成 | "Video is live", "Content published" |
| 婉拒合作 | 明确或委婉拒绝 | "Not interested", "Doesn't fit my brand" |
| 自动回复 | 非本人真实回复 | "Out of office", "Auto reply" |
| 未知 | 意图不明确或异常 | 无法归类的情况 |

### 2.4 Stage流程定义

```
初次联系 → 意图探索 → 价格谈判 → 合作确认 → 样品发送 → 内容创作 → 内容审核 → 项目完成

```

| **阶段值** | **含义** | **触发条件** |
| --- | --- | --- |
| 初次联系 | 刚发送合作邀请 | 项目创建时的初始状态 |
| 意图探索 | 网红表现出初步兴趣 | 网红回复过邮件 |
| 价格谈判 | 进入商务条件讨论 | 使用价格谈判工具发送邮件 |
| 合作确认 | 双方达成合作协议 | scene="确认合作" |
| 样品发送 | 产品样品已寄出 | 使用物流通知发送邮件 |
| 内容创作 | 网红制作推广内容 | scene="样品确认"/使用脚本工具发送邮件 |
| 内容审核 | 检查内容质量合规 | scene="内容完成" |
| 项目完成 | 合作顺利结束 | 内容审核通过当最后的邮件意图为内容完成，场景是内容审核时，在这封邮件上有一个专属的图标【审核完成】此时更新阶段为项目完成。 |

## LLM Prompt模板

```
你是一名跨境品牌合作流程专家，专注于自动化理解和推进与网红的邮件往来。
任何时候，项目阶段只能向后推进，绝不可回退到更早阶段。

【输入信息】
- 历史邮件（thread_history）
- 当前邮件（current_email）
- 网红档案（influencer_profile）
- 产品信息（product_info）
- 当前阶段（current_project_stage）

【阶段顺序（不可逆）】
1. 初次联系
2. 意图探索
3. 价格谈判
4. 合作确认
5. 样品发送
6. 内容创作
7. 内容审核
8. 项目完成
（如无法判断，用“未知”；但绝不能回到比 current_project_stage 更早的序号）

【任务】
1. scene —— 识别当前邮件的主要合作意图
2. stage —— 在保证“阶段不回退”的前提下，结合全部上下文给出最新项目阶段
3. summary —— 30-50 字中文，自然语言概括邮件要点与下一步建议

【输出格式】仅返回以下 JSON，勿添加其他字符：

json
{
  "scene": "<积极合作|探索沟通|报价回复|确认合作|样品确认|内容完成|婉拒合作|自动回复|未知>",
  "stage": "<初次联系|意图探索|价格谈判|合作确认|样品发送|内容创作|内容审核|项目完成|未知>",
  "summary": "<30-50字自然语言摘要，含下一步建议>"
}

```

# ***邮件内容生成功能需求规范***

## 功能概述

提供统一的AI邮件生成服务，根据合作阶段、邮件类型和上下文信息，自动生成专业、自然的英文商务邮件内容。

### 1.1 统一接口原则

- **单一接口**：所有邮件类型共用一个API接口
- **类型区分**：通过`email_type`参数区分不同的邮件场景
- **上下文感知**：结合项目阶段、历史记录和当前邮件内容

### 1.2 邮件类型定义

| **类型值** | **中文名称** | **使用场景** | **必需参数** |
| --- | --- | --- | --- |
| first_contact | 初次建联 | 首次联系网红 | product_info |
| quote_negotiation | 价格谈判 | 回应网红报价 | budget_range |
| shipping_notice | 发货通知 | 样品寄出通知 | shipping_info |
| script_suggestion | 脚本建议 | 内容创作指导 | script_guideline |
| other | 自定义邮件 | 其他业务场景 | 222 |

## 输入参数规范

### 2.1 完整输入结构

```
{
  "email_type": "quote_negotiation",
  "scene": "报价回复",
  "stage": "价格谈判",
  "history_summaries": [
    "发送初次合作邀请，介绍产品特性和合作意向",
    "网红表达兴趣并询问更多产品细节",
    "提供产品详细信息和样品寄送意向"
  ],
  "current_email": {
    "subject": "Re: Collaboration Opportunity",
    "content": "Thanks for the details! My rate for this type of collaboration is $800. Let me know if this works for you.",
    "timestamp": "2024-06-16 09:15:00"
  },
  "influencer_name": "Alex",
  "influencer_style": "tech review / English",
  "product_info": {
    "name": "智能运动耳机XR-1",
    "brand": "TechFit",
    "key_features": "20小时续航、心率监测、IPX7防水",
    "price": "$199",
    "target_audience": "运动爱好者、健身达人"
  },
  "budget_range": "$600-800",
  "shipping_info": {
    "carrier": "DHL Express",
    "tracking_number": "1234567890",
    "estimated_delivery": "2024-06-20"
  },
  "script_guideline": {
    "video_type": "开箱测评",
    "duration": "3-5分钟",
    "key_points": ["外观设计", "功能演示", "运动场景测试"],
    "style_preference": "专业测评风格，注重细节展示"
  },
  "extra_context": "用户自定义的特殊要求或补充信息"
}

```

### 2.2 字段说明

### 核心字段（必填）

- **email_type**: 邮件类型，决定生成逻辑
- **scene**: 当前邮件的业务场景，影响语气和重点
- **stage**: 项目当前阶段，决定上下文背景
- **history_summaries**: 历史邮件摘要数组，避免重复沟通
- **current_email**: 对方最新邮件完整内容，用于引用回应
- **influencer_name**: 网红姓名，用于邮件称呼
- **product_info**: 产品基本信息

### 场景专用字段（按需填写）

- **budget_range**: 预算范围（price_negotiation场景）
- **shipping_info**: 物流信息（shipping_notice场景）
- **script_guideline**: 脚本要求（script_suggestion场景）
- **extra_context**: 自定义需求（other场景，以及用户在生成前写的额外信息)

### 辅助字段（可选）

- **influencer_style**: 网红内容风格，用于调整邮件语调

## 输出格式规范

### 3.1 标准输出结构

```
{
  "influencer_id": "alex_tech_review_001",
  "influencer_name": "Alex",
  "email_subject": "Re: Collaboration Opportunity - Let's Find a Win-Win",
  "email_body": "Hi Alex,\n\nThanks for your quick response! I really appreciate your interest in working with us.\n\n[邮件正文内容]\n\nLooking forward to hearing your thoughts!\n\nBest regards,\n[Name]"
}

```

### 3.2 内容质量要求

### 基本规范

- **语言**: 全英文输出
- **长度**: 邮件正文 ≤ 220英文单词
- **格式**: 纯文本，不使用Markdown格式
- **语调**: 友好自然，避免模板化表达

### 禁止内容

- 不得出现"As an AI"等AI身份暗示
- 避免过度夸张的营销语言
- 不使用明显的模板化句式

### 引用规范

- 引用对方邮件内容时，单次引用 ≤ 15英文单词
- 引用内容需加引号标识
- 引用要自然融入邮件上下文
1. 邮件生成逻辑

### 3.3 动态内容策略

### 根据历史避免重复

- 分析history_summaries判断已沟通内容
- 已介绍过的产品信息可简化或省略
- 已确认的合作细节无需重复说明
- 传入的字段比较动态，如果不想精细调可以全量提交最新的。例如 stage 在初次建联阶段，那就传空值。scene除了初次建联是初次建联外，后续按照对方最新邮件写入。

## LLM Prompt模板

```
你是一名资深网红营销专员，擅长撰写走心且高转化的英文商务邮件。
请根据输入 JSON 生成 **1 封专属英文邮件**，并 **仅以 JSON** 形式输出（禁止输出其它文本）。

---

## 1 输入字段

| 字段 | 必/选 | 说明 |
|------|------|------|
| email_type | **必填** | `"first_contact" \| "quote_negotiation" \| "shipping_notice" \| "script_suggestion" \| "other"` |
| scene | 必填 | 如 `"探索沟通"`、`"报价回复"`…，辅助判断语气 & 重点 |
| stage | 选填 | 如 `"价格谈判"`、`"样品发送"`…，判定合作进度 |
| history_summaries | 必填 | 最近若干封邮件的摘要数组，按时间顺序 |
| current_email | 必填 | 对方最新来信全文，便于引用/回应 |
| influencer_name | **必填** | 达人称呼 |
| influencer_style | 选填 | 达人内容风格 / 主语言 (示例 `"tech review / English"`) |
| product_info | **必填** | 产品卖点、品牌名称，可附 URL |
| budget_range | 选填 | 预算区间；quote_negotiation 场景常用 |
| shipping_info | 选填 | 物流公司 + 单号 + ETA；shipping_notice 场景常用 |
| script_guideline | 选填 | 脚本要求；script_suggestion 场景常用 |
| extra_context | 选填 | 用户自定义补充指令、特殊偏好等 |

---

## 2 输出格式（严格 JSON）

```json
{
  "influencer_id": "<string>",
  "influencer_name": "<string>",
  "email_subject": "<string>",
  "email_body": "<string>"
}

⸻

3 写信动态指南

整体要求
        •        全英文；语气友好、自然，避免模板化 & 夸张宣传
        •        email_body ≤ 220 英文词；不使用 Markdown 符号；不得出现 “As an AI” 等表述
        •        根据 stage / history_summaries 判断哪些信息已沟通过，避免重复啰嗦
        •        如需引用 current_email，务必自然简短（≤ 15 词）

建议段落结构（按需增删，顺序可调）
first_contact（初次建联）
开头：具体夸赞网红内容（1-2句）
主体：品牌介绍 + 产品核心卖点
契合点：说明为什么选择该网红
结尾：询问合作兴趣
quote_negotiation（价格谈判）
开头：感谢回复并认可网红价值
主体：说明预算范围和让步理由
价值点：强调长期合作潜力
结尾：邀请进一步讨论
shipping_notice（发货通知）
开头：确认合作并表达期待
主体：详细物流信息（快递公司、单号、预计到达）
说明：收货注意事项
结尾：询问收货确认
script_suggestion（脚本建议）
开头：确认样品收到
主体：2-3个具体的视频创意方向
风格：结合网红风格和产品特性
结尾：表达期待并提供支持
other（自定义邮件）
完全基于extra_context中的用户需求
保持专业商务邮件格式
结合当前项目上下文
⸻
按照以上规范生成邮件，并仅输出符合 “输出格式” 的 JSON。

```

# 其他内容补充

在建联详情记录中的达人区域显示以下字段标签

| overallPersonaAndStyle | 综合人设/风格， [AI 生成] 概括该达人在所有平台的整体形象和风格 （如 "专业科技测评师"， "轻松幽默生活博主"）。 |
| --- | --- |
| mainAudience | 主要受众画像，**[AI 生成]** 整合所有平台分析结果，描述核心受众特征 （如 "以美国年轻男性为主（18-34 岁）"）。 |
| potentialBrandType | 潜在合作品牌类型，**[AI 生成]** 基于内容、风格和受众，推测适合合作的品牌品类列表 （如 ["数码产品"， "快时尚"， "生活用品"]）。 |
| influencerEval | 达人总体评价，**[AI 生成]**  "综合评估达人在该平台的内容质量、互动表现、独特性和发展潜力 （例如：内容质量高，互动积极，风格独特，潜力巨大 / 内容同质化，互动一般，有待提升 / 更新频率低，影响力有限 等）"， |