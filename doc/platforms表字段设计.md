# Platforms表字段设计文档

## 1. 达人数据预览表（基础信息+核心指标）

| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| subscribers | Number | 粉丝数 | 已有字段 |
| lastPostDate | Date | 最近发布时间 | 通过分析最近内容数据中的最新发布时间得出 |
| lastPromotionDate | Date | 最近推广时间 | 通过分析品牌合作数据中的最新推广时间得出 |
| noxScore | Number | NOX评分 | 综合考虑活跃度、商业价值、受众质量等多个维度的加权评分 |
| cooperationTendency | String | 合作倾向 | 基于历史推广频率、品牌合作类型、价格接受度等因素综合评估 |
| engagementRate | Number | 互动率 | 已有字段 |
| avgViews | Number | 平均观看量 | 通过分析最近30天视频播放数据计算平均值 |
| avgLikes | Number | 平均点赞数 | 通过分析最近30天视频点赞数据计算平均值 |
| totalVideos | Number | 总视频数 | 通过统计达人发布的所有视频内容得出 |
| videoFrequency | Number | 发布频率(天) | 通过分析最近30天的发布时间间隔计算平均值 |

## 2. 数据总览表（统计指标和排名）

| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| avgViews | Number | 平均观看量 | 通过分析最近30天视频播放数据计算平均值 |
| avgLikes | Number | 平均点赞数 | 通过分析最近30天视频点赞数据计算平均值 |
| avgComments | Number | 平均评论数 | 通过分析最近30天视频评论数据计算平均值 |
| avgShares | Number | 平均分享数 | 通过分析最近30天视频分享数据计算平均值 |
| engagementRate | Number | 互动率 | 已有字段 |
| viewsGrowthRate | Number | 观看量增长率 | 对比最近30天和前30天的观看量数据计算增长率 |
| subscribersGrowthRate | Number | 粉丝增长率 | 对比最近30天和前30天的粉丝数据计算增长率 |
| likesGrowthRate | Number | 点赞增长率 | 对比最近30天和前30天的点赞数据计算增长率 |
| commentsGrowthRate | Number | 评论增长率 | 对比最近30天和前30天的评论数据计算增长率 |
| qualityScore | Number | 质量评分 | 综合考虑内容质量、观众留存、互动深度等维度的评分 |
| categoryRanking | Number | 分类排名 | 在同类达人中的排名位置 |
| overallRanking | Number | 总体排名 | 在所有达人中的排名位置 |
| globalFollowersRanking | Number | 全球粉丝数排名 | 在全球所有达人中的粉丝数排名 |
| globalFollowersRankingPercent | Number | 全球粉丝数排名百分比 | 在全球所有达人中的粉丝数排名百分比 |
| regionalFollowersRanking | Number | 地区粉丝数排名 | 在所在地区达人中的粉丝数排名 |
| regionalFollowersRankingPercent | Number | 地区粉丝数排名百分比 | 在所在地区达人中的粉丝数排名百分比 |
| noxRadarChart | Object | NOX评分雷达图 | 包含粉丝增长、创作频率、视频质量、互动率、粉丝可信度五个维度的评分 |
| cooperationBehaviorTags | Array | 合作行为标签 | 描述达人合作行为特征的标签列表 |
| cpmPriceRange | String | CPM价格区间 | 千次展示成本的价格描述 |
| implantVideoPrice | String | 植入视频价格 | 植入视频的价格区间 |
| trendData | Array | 趋势数据 | 包含粉丝增长、观看量增长、互动率变化等时间序列数据 |
| noxScoreHistory | Array | NOX评分历史 | 记录NOX评分的历史变化趋势 |
| viewsDistribution | Object | 观看量分布 | 不同观看量区间的视频数量分布 |

## 3. 受众数据表（粉丝画像和质量分析）

| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| audienceGender | Object | 受众性别 | 已有字段 |
| audienceAge | Object | 受众年龄 | 已有字段 |
| audienceAgeDetailed | Object | 受众年龄详细分布 | 包含13-17、18-24、25-34、35-44、45-54、55-64、65+各年龄段的具体百分比 |
| audienceLocation | Object | 受众地区分布 | 通过分析粉丝地理位置数据统计各地区占比 |
| audienceLanguage | Object | 受众语言分布 | 通过分析粉丝语言设置和评论语言统计分布 |
| audienceInterests | Array | 受众兴趣标签 | 通过分析粉丝关注的其他账号和互动内容提取兴趣标签 |
| audienceInterestCategories | Object | 受众兴趣分类 | 包含彩妆、偶像、电视&动画、电脑配件、宠物&动物等兴趣分类的分布 |
| audienceDevice | Object | 受众设备分布 | 通过分析观看设备类型统计分布 |
| audienceActivity | Object | 受众活跃时间 | 通过分析粉丝在线时间和互动时间统计活跃时段 |
| audienceQualityScore | Number | 受众质量评分 | 综合考虑粉丝活跃度、真实性、购买力等因素评分 |
| fakeFollowersRatio | Number | 僵尸粉比例 | 通过分析粉丝账号活跃度、注册时间、互动行为等判断僵尸粉比例 |
| audienceRetention | Number | 受众留存率 | 通过分析粉丝关注时长和取关率计算留存率 |
| audienceEngagement | Number | 受众互动度 | 通过分析粉丝点赞、评论、分享等行为计算互动度 |
| audienceOverlap | Array | 受众重叠分析 | 与其他类似达人的粉丝重叠度分析 |
| audienceBrandAffinity | Object | 受众品牌倾向 | 通过分析粉丝对不同品牌的互动和提及分析品牌倾向 |
| marketingAnalysis | Object | 营销分析 | 包含正向反馈受众、消费影响力、推广吸引度、推广专业度等指标 |
| positiveAudienceRatio | Number | 正向反馈受众比例 | 对推广内容有正向反馈的受众占比 |
| consumptionInfluence | Number | 消费影响力 | 受众的消费决策影响力评分 |
| promotionAttraction | Number | 推广吸引度 | 推广内容对受众的吸引力评分 |
| promotionProfessionalism | Number | 推广专业度 | 推广内容的专业度评分 |
| audienceCredibilityBreakdown | Object | 受众可信度分解 | 包含普通粉、可疑粉、僵尸粉、网红粉的具体百分比 |
| realAudienceRatio | Number | 真实受众比例 | 真实受众占比百分比 |

## 4. 内容数据表（内容表现和发布分析）

| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| totalVideos | Number | 总视频数 | 通过统计达人发布的所有视频内容得出 |
| avgViews | Number | 平均观看量 | 通过分析最近30天视频播放数据计算平均值 |
| avgLikes | Number | 平均点赞数 | 通过分析最近30天视频点赞数据计算平均值 |
| avgComments | Number | 平均评论数 | 通过分析最近30天视频评论数据计算平均值 |
| avgShares | Number | 平均分享数 | 通过分析最近30天视频分享数据计算平均值 |
| avgDuration | Number | 平均视频时长 | 通过分析视频时长数据计算平均值 |
| videoFrequency | Number | 发布频率(天) | 通过分析最近30天的发布时间间隔计算平均值 |
| peakPostTime | String | 最佳发布时间 | 通过分析不同时间段发布内容的表现数据得出最佳时间 |
| topPerformingVideos | Array | 热门视频列表 | 按观看量、互动率等指标排序的热门视频 |
| contentTypes | Object | 内容类型分布 | 不同内容类型的视频数量和表现分布 |
| contentCategories | Object | 内容分类分布 | 不同内容分类的视频数量和表现分布 |
| hashtagUsage | Array | 标签使用情况 | 常用标签及其使用频率和效果分析 |
| contentTrends | Array | 内容趋势分析 | 不同内容主题的热度变化趋势 |
| videoQualityScore | Number | 视频质量评分 | 综合考虑画质、音质、剪辑等技术指标的质量评分 |
| contentConsistency | Number | 内容一致性 | 通过分析内容风格、主题一致性等计算一致性评分 |
| viralContent | Array | 病毒式传播内容 | 传播效果特别好的内容分析 |
| contentEngagement | Object | 内容互动分析 | 不同内容类型的互动效果对比 |
| publishingCalendar | Object | 发布日历 | 月历形式的发布时间分布分析 |
| weeklyPublishingPattern | Object | 每周发布模式 | 一周内各天发布频率的分布 |
| publishingFrequencyAnalysis | Object | 发布频率分析 | 每周发布的具体分布情况 |
| tagCloudData | Array | 标签云数据 | 词云形式的标签展示数据 |
| topHashtags | Array | 热门标签排行 | 前N个热门标签及其使用频率百分比 |
| videoListData | Array | 视频列表数据 | 包含视频缩略图、时长、观看数、点赞数、评论数等详细信息 |
| engagementRateBreakdown | Object | 互动率细分 | 包含观看量占比、点赞数/观看量、评论数/观看量、分享数/观看量等细分指标 |
| interactionTimeChart | Object | 互动时间图表 | 点赞、评论、分享、观看量的时间序列图表数据 |
| contentPerformanceRanking | Object | 内容表现排名 | 各维度内容表现的排名对比 |

## 5. 品牌数据表（商业合作和品牌分析）

| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| promotedProductCategory | String | 推广产品类别 | 已有字段 |
| totalBrandCollaborations | Number | 总合作品牌数 | 通过分析历史内容中的品牌合作数量统计 |
| recentBrandCollaborations | Array | 近期合作品牌 | 最近3个月内合作的品牌列表 |
| brandCollaborationFrequency | Number | 品牌合作频率 | 通过分析品牌合作的时间间隔计算平均频率 |
| avgBrandPostPerformance | Number | 品牌内容平均表现 | 品牌合作内容的平均观看量、互动率等指标 |
| brandLoyalty | Number | 品牌忠诚度 | 通过分析与同一品牌的多次合作情况计算忠诚度 |
| brandCategories | Object | 合作品牌类别分布 | 不同品牌类别的合作数量和效果分布 |
| brandMentionFrequency | Number | 品牌提及频率 | 在非合作内容中提及品牌的频率 |
| brandSentiment | Object | 品牌情感分析 | 对不同品牌的情感倾向分析 |
| commercialContentRatio | Number | 商业内容占比 | 商业推广内容在总内容中的占比 |
| brandCollaborationCPM | Number | 品牌合作CPM | 品牌合作的千次展示成本 |
| brandCollaborationEngagement | Number | 品牌内容互动率 | 品牌合作内容的平均互动率 |
| brandCollaborationROI | Number | 品牌合作ROI | 品牌合作的投资回报率评估 |
| brandPreferences | Array | 品牌偏好分析 | 基于合作历史和内容分析得出的品牌偏好 |
| competitorBrandAnalysis | Object | 竞品品牌分析 | 与竞争对手的品牌合作对比分析 |
| brandGrowthPotential | Number | 品牌增长潜力 | 基于当前表现和趋势评估的品牌合作增长潜力 |
| brandAdEffectiveness | Number | 品牌广告效果 | 品牌广告的互动率效果数值 |
| monthlyBrandPostFrequency | String | 月度品牌发布频次 | 每月发布品牌内容的频次描述 |
| brandVsNonBrandPerformance | Object | 品牌vs非品牌内容表现 | 推广内容和非推广内容的观看量对比图表数据 |
| brandCollaborationTimeline | Object | 品牌合作时间线 | 品牌合作的时间序列图表数据 |
| brandPartnershipDetails | Array | 品牌合作详情 | 包含品牌名称、合作次数、互动率、总观看量、最近合作时间、收益等详细信息 |
| brandRevenueData | Array | 品牌收益数据 | 各品牌合作的具体收益金额数据 |
| brandEngagementComparison | Object | 品牌互动率对比 | 不同品牌合作内容的互动率对比分析 |
| brandCollaborationTrends | Object | 品牌合作趋势 | 品牌合作数量和效果的时间趋势分析 |
| sponsoredContentRatio | Number | 赞助内容占比 | 赞助推广内容在总内容中的占比 |
| brandPartnershipROI | Object | 品牌合作ROI分析 | 各品牌合作的投资回报率详细分析 |