# 达人数据更新系统需求规范

## **功能概述**

建立智能化的达人数据更新机制，通过活跃度分析、业务价值评估和优先级排序，实现资源的精准投入和数据的动态维护，同时支持新达人的自动发现和匹配。

## **核心概念定义**

### 2.1 达人更新优先级分数

**定义**：基于达人活跃度和业务价值的综合评分机制 **作用**：决定数据更新频率和资源分配优先级 **计算公式**：`总分 = 活跃状态分 + 业务价值分 + 额外加成分`

### 2.2 数据更新周期

**定义**：根据优先级分数确定的数据抓取频率 **规则**：每周更新一轮≥10分的达人，0分以下停止更新 **基准时间**：以每周一开始时的分数为准，周期内不动态调整

### 2.3 新达人发现机制

**定义**：通过竞品分析和热门标签爬虫自动发现潜在合作达人 **目标**：扩充达人库，提高匹配成功率

## 评分体系规范

### 3.1 活跃状态评分

### 评分标准（基于近30天发布数）

| **发布数量** | **活跃状态分** | **活跃等级** |
| --- | --- | --- |
| ≥5条 | 50分 | 超活跃 |
| 4条 | 40分 | 活跃 |
| 3条 | 30分 | 正常 |
| 2条 | 20分 | 低活跃 |
| 1条 | 10分 | 低活跃 |
| 0条 | 0分 | 不活跃 |

### 技术实现要求

- **数据源**：RSS链接 + 平台API
- **统计周期**：滚动30天窗口
- **更新频率**：每周一凌晨重新计算

### 3.2 业务价值评分

### 评分标准

| **达人类型** | **业务价值分** | **判断条件** | **数据验证** |
| --- | --- | --- | --- |
| 合作成功达人 | 50分 | 近30天建联记录状态="已完成" | 建联系统状态同步 |
| 有回复达人 | 30分 | 近30天有回复过邮件达人 | 邮件系统状态同步 |
| 用户发送过邮件 | 10分 | 近7天算法推荐过给用户 | 推荐系统记录查询 |
| 无互动达人 | 0分 | 无以上任何记录 | 默认基础分 |

### 业务规则

- **互斥性**：同一达人只取最高分，不累加
- **时效性**：超过统计周期的记录不计分

### 3.3 额外加成规则

### 加成项目

| **特殊情况** | **加成分数** | **判断条件** | **计算逻辑** |
| --- | --- | --- | --- |
| 近期活跃 | +10分 | 近14天有作品发布 | 保持内容更新活跃度 |
| 百万大号 | +20分 | 粉丝数>100万 | 影响力优先 |
| 十万达人 | +10分 | 粉丝数>10万且≤100万 | 粉丝量加成 |
| 可能休眠 | -100分 | 连续30天无发布 | 休眠账号降权 |

### 加成计算规则

- **累加性**：所有符合条件的加成项目可以累加
- **上限控制**：单个达人总加成不超过+50分
- **下限保护**：最终分数不低于0分

## 更新策略规范

### 4.1 分组更新策略

### 优先级分组

```
高优先级（≥30分）：每周2次更新
中优先级（10-29分）：每周1次更新
低优先级（1-9分）：每2周1次更新
暂停更新（0分）：停止自动更新

```

### 更新排队机制

```
周一 00:00 → 计算所有达人分数
周一 01:00 → 按分数从高到低排序
周一-周日 → 按优先级顺序执行更新
下周一重新计算分数和排序

```

### 4.2 数据更新内容

### 必更新字段

- **基础信息**：粉丝数、关注数、简介
- **最新内容**：近期视频标题、发布时间、互动数据
- **联系方式**：邮箱地址、社交媒体链接
- **内容标签**：AI提取的内容分类标签

### 可选更新字段

- **历史数据**：全量视频信息（低频更新）
- **粉丝画像**：受众分析数据（月度更新）
- **合作记录**：品牌合作历史（手动更新）

### 4.3 YouTube RSS源数据获取

### RSS源发现机制

YouTube为每个频道提供标准RSS源，获取方式：

**方法1：通过频道ID获取**

```
RSS URL格式：https://www.youtube.com/feeds/videos.xml?channel_id={CHANNEL_ID}
频道ID获取：从频道主页URL或页面源码中提取

```

**方法2：通过用户名获取（旧版频道）**

```
RSS URL格式：https://www.youtube.com/feeds/videos.xml?user={USERNAME}
适用于早期注册的YouTube频道

```

**方法3：自动检测与验证**

python

```
# 伪代码示例
def get_youtube_rss(channel_url):
# 1. 从频道页面提取channel_id
    channel_id = extract_channel_id(channel_url)

# 2. 构建RSS URL
    rss_url = f"https://www.youtube.com/feeds/videos.xml?channel_id={channel_id}"

# 3. 验证RSS有效性
    if validate_rss_feed(rss_url):
        return rss_url
    else:
# 降级为备用方案
        return fallback_method(channel_url)

```

### RSS数据解析流程

```
1. RSS源监控 → 定时轮询RSS feeds
2. 新视频检测 → 对比上次获取的video_id
3. 基础信息提取 → 标题、发布时间、缩略图、视频链接
4. 数据库存储 → 记录到内容库video_content表
5. 爬虫任务队列 → 添加详细信息补充任务

```

### RSS解析数据结构

xml

```
<!-- YouTube RSS Feed示例 -->
<entry>
    <id>yt:video:VIDEO_ID</id>
    <yt:videoId>VIDEO_ID</yt:videoId>
    <title>视频标题</title>
    <link href="https://www.youtube.com/watch?v=VIDEO_ID"/>
    <author>
        <name>频道名称</name>
        <uri>https://www.youtube.com/channel/CHANNEL_ID</uri>
    </author>
    <published>2024-06-15T10:30:00+00:00</published>
    <updated>2024-06-15T10:30:00+00:00</updated>
    <media:group>
        <media:title>视频标题</media:title>
        <media:description>视频描述（截断版）</media:description>
        <media:thumbnail url="缩略图URL"/>
    </media:group>
</entry>

```

### 爬虫补充数据机制

***调用 youtubeapi 补充***

```
RSS获取基础信息后，通过 youtubeapi补充：
1. 视频详细描述 → 完整description文本
2. 视频统计数据 → 观看数、点赞数、评论数
3. 评论内容抓取 → 热门评论

```

### 技术实现要点

- **并发控制**：RSS轮询和爬虫任务分离，避免阻塞
- **增量更新**：只处理新发布的视频，提高效率

**设置抓取延迟在发布后的3 天，视频发布后 3 天**

## 新达人发现机制

### 5.1 竞品分析模式

### 工作流程

```
用户提交商品 → AI提取关键词 → 搜索竞品合作达人 → 分析其合作网红 → 扩展相似达人

```

### 关键词提取规则

| **商品信息** | **提取类型** | **示例** |
| --- | --- | --- |
| 商品名称 | 品类词 + 功能词 | "iPhone手机壳" → ["phone case", "protection"] |
| 商品描述 | 场景词 + 特性词 | "运动防水" → ["sports", "waterproof"] |
| 商品标签 | 目标人群 | "年轻人" → ["young adults", "millennials"] |

### Prompt

```
你是一个社交媒体标签专家，根据用户提交的商品清单，生成用于YouTube和TikTok搜索网红达人的标签。

## 输入
商品名称列表: {product_names}

## 任务
1. 分析商品类型和特点
2. 生成竞品品牌和产品相关的搜索标签
3. 标签要适合在YouTube/TikTok搜索网红内容
4. 输出的标签为英文，数量为30个

## 输出格式
严格JSON格式，不要其他文本：
{
  "search_tags": [
    "product review",
    "brand comparison",
    "unboxing video",
    "竞品品牌名称",
    "产品类别关键词"
  ]
}

```

输入：商品标题，放入最近100个

输出：搜索标签，去重历史查过的

### 5.2 热门标签爬虫

**通过时下流行获得网红，同之前的爬虫方案**

### 标签发现策略

- **平台热门**：YouTube/Instagram/TikTok趋势标签
- **AI增强**：使用web search工具获取实时热门话题
- **商品关联**：将热门标签与商品库进行智能匹配

### prompt

```
你是一个社交媒体趋势分析师，需要搜索当前热门话题并生成用于发现网红达人的标签。

## 任务
1. 搜索最近7天的热门话题、流行趋势
2. 重点关注消费类产品、科技、生活方式相关话题
3. 生成适合在YouTube/TikTok搜索的标签

## 输出格式
严格JSON格式：
{
  "trending_tags": [
    "当前热门话题标签",
    "流行产品类别",
    "热门hashtag"
  ]
}

请现在开始搜索并生成标签。

```

### 5.3 新增达人enhance tag增强prompt

**请使用gpt4.1模型，4o的幻觉会生成不存在的标签**

```
### 任务说明
你是一名电商内容分析师，需要根据**同一位达人**的多条视频文本信息，综合判断该达人整体最适合推广哪些商品分类。\
**商品分类列表**已整理如下
Home Supplies
Kitchenware
Textiles & Soft Furnishings
Household Appliances
Womenswear & Underwear
Muslim Fashion
Shoes
Beauty & Personal Care
Phones & Electronics
Computers & Office Equipment
Pet Supplies
Baby & Maternity
Sports & Outdoor
Toys & Hobbies
Furniture
Tools & Hardware
Home Improvement
Kids’ Fashion
Menswear & Underwear
Luggage & Bags
Collectibles
Jewelry Accessories & Derivatives
Automotive & Motorcycle
Fashion Accessories
Food & Beverages
Health
Books, Magazines & Audio
Home Organizers
Bathroom Supplies
Home Decor
Home Care Supplies
Laundry Tools & Accessories
Festive & Party Supplies
Miscellaneous Home
Tea & Coffeeware
Kitchen Knives
Barbecue
Bar & Wine Utensils
Bakeware
Cookware
Cutlery & Tableware
Drinkware
Kitchen Utensils & Gadgets
Bedding and Linens
Household Textiles
Fabrics & Sewing Supplies
Kitchen Appliances
Home Appliances
Large Home Appliances
Commercial Appliances
Women’s Tops
Women’s Bottoms
Women’s Dresses
Women’s Special Clothing
Women’s Suits & Sets
Women’s Underwear
Women’s Sleepwear & Loungewear
Hijabs
Women’s Islamic Clothing
Men’s Islamic Clothing
Outerwear
Islamic Accessories
Prayer Attire & Equipment
Islamic Sportswear
Umroh Equipment
Women’s Shoes
Men’s Shoes
Shoe Accessories
Makeup
Skincare
Haircare & Styling
Hand, Foot & Nail Care
Bath & Body Care
Men’s Care
Personal Care Appliances
Eye & Ear Care
Nasal & Oral Care
Special Personal Care
Mobile Phone Accessories
Cameras & Photography
Audio & Video
Gaming & Consoles
Smart & Wearable Devices
Universal Accessories
Tablet & Computer Accessories
Desktop Computers, Laptops & Tablets
Desktop & Laptop Components
Computer Accessories
Data Storage & Software
Network Components
Office Equipment
Office Stationery & Supplies
Dog & Cat Food
Dog & Cat Furniture
Dog & Cat Clothing
Dog & Cat Litter
Dog & Cat Grooming
Dog & Cat Healthcare
Dog & Cat Accessories
Fish & Aquatic Supplies
Reptile & Amphibian Supplies
Bird Supplies
Small Animal Supplies
Farm Animal & Poultry Supplies
Baby Clothing & Shoes
Nursing & Feeding
Baby Care & Health
Maternity Supplies
Baby Fashion Accessories
Sport & Outdoor Clothing
Sports Footwear
Sports & Outdoor Accessories
Ball Sports
Water Sports
Winter Sports
Fitness
Camping & Hiking
Leisure & Outdoor Recreation Equipment
Swimwear, Surfwear & Wetsuits
Fan Shop
Lawn Games
Educational Toys
Games & Puzzles
Classic & Novelty Toys
Musical Instruments & Accessories
DIY
Indoor Furniture
Outdoor Furniture
Commercial Furniture
Power Tools
Hand Tools
Measuring Tools
Garden Tools
Soldering Equipment
Tool Organizers
Hardware
Pumps & Plumbing
Solar & Wind Power
Lights & Lighting
Electrical Equipment & Supplies
Kitchen Fixtures
Building Supplies
Bathroom Fixtures
Security & Safety
Garden Supplies
Boys’ Clothes
Girls’ Clothes
Men’s Tops
Men’s Bottoms
Men’s Special Occasion Clothing
Men’s Underwear & Socks
Men’s Sleepwear & Loungewear
Men’s Suits & Sets
Women’s Bags
Men’s Bags
Luggage & Travel Bags
Functional Bags
Bag Accessories
Cultural Items
Trading Cards & Accessories
Sports Collectibles
Entertainment
Platinum, Carat Gold
Silver
Natural Crystal
Non-natural Crystal
Jade
Semiprecious Stones
Artificial Gemstones
Pearl
Amber
Mellite
Auto Replacement Parts
Motorcycle Parts
Car Electronics
Car Exterior Accessories
Car Interior Accessories
Car Repair Tools
Car Lights
Quads, Motorhomes & Boats
Car Washing & Maintenance
Motorcycle Accessories
Hair Extensions & Wigs
Dressmaking Fabrics
Wedding Accessories
Clothes Accessories
Eyewear
Fashion Watches & Accessories
Costume Jewelry & Accessories
Hair Accessories
Milk & Dairy
Beer, Wine & Spirits
Drinks
Pantry Food
Staples & Cooking Essentials
Baking
Snacks
Nutrition & Wellness
Medical Supplies
Alternative Medications & Treatments
Humanities & Social Sciences
Magazines & Newspapers
Literature & Art
Economics & Management
Children’s & Infants’ Books
Science & Technology
Lifestyle & Hobbies
Education & Schooling
Video & Music
---

### 输出要求
1. 针对**整位达人**，仅输出一组 JSON 数组，形如
   `["分类1","分类2","分类3"]`
   （若无法匹配则输出 `[]`）。
2. **不要输出**除该数组外的任何字符。
3. 每个分类必须完整、精确地来自上表。
4. 若达人有多条视频，请先合并信息后再决定标签，避免按视频逐条输出。

---

### 推荐思考步骤（仅供内部推理，**不要**写入最终答案）
1. **整合信息**
   - 将输入数组中所有视频的 *title*、*description*、*commentContent*、*topComments* 合并，提取高频或强相关关键词。
2. **推断受众意图与使用场景**
   - 判断粉丝可能的兴趣点与购买场景（如健身、露营、数码等）。
3. **匹配商品分类**
   - 在上表中选出最贴切的 3 个分类；若同一大类下已有更细分的二级分类，以细分优先。
4. **置信度排序**
   - 按与你推断到的主题匹配度从高到低排序。
5. **生成唯一输出**
   - 每位达人仅输出一次 `enhancetags`，即使输入含多条视频。

---
⸻

开始分析并输出 enhancetags：

```

### 5.5 增强标签数据表

## 标签表设计 `crawl_tags`

| **字段名** | **数据类型** | **长度** | **是否必填** | **描述** |
| --- | --- | --- | --- | --- |
| _id | String | - | 是 | 标签唯一标识，MongoDB 自动生成 |
| tagName | String | 100 | 是 | 标签名称，如 "phone case", "tech review" |
| createdAt | Date | - | 是 | 标签创建/添加时间 |
| discoveredInfluencers | Number | - | 是 | 通过该标签发现的达人总数，默认0 |
| crawlStatus | String | 20 | 是 | 爬取状态：pending（待爬取）/ crawling（爬取中）/ completed（已完成）/ failed（失败） |